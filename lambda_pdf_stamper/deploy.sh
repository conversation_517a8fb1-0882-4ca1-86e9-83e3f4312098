#!/bin/bash

# Configuración
AWS_REGION="us-east-1"
ECR_REPOSITORY_NAME="lambda-pdf-stamper"
LAMBDA_FUNCTION_NAME="pdf-stamper-lambda"
LAMBDA_ROLE_NAME="lambda-pdf-stamper-role"

# Variables de entorno para la Lambda
API_KEY="PDFStamper2024SecureKey"
S3_ENDPOINT_URL="https://s3.amazonaws.com"
S3_ACCESS_KEY="DUMMY_ACCESS_KEY_REPLACE"
S3_SECRET_KEY="DUMMY_SECRET_KEY_REPLACE"
S3_REGION="us-east-1"
PDF_INPUT_DIR="documentos"
LAMBDA_TIMEOUT="300"
LAMBDA_MEMORY="1024"

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Iniciando despliegue de Lambda PDF Stamper...${NC}"

# Obtener ID de cuenta AWS
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
if [ -z "$ACCOUNT_ID" ]; then
    echo -e "${RED}❌ Error: No se pudo obtener el ID de cuenta AWS${NC}"
    exit 1
fi

ECR_URI="${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}"

# Verificar si el repositorio ECR existe
echo -e "${YELLOW}📦 Verificando repositorio ECR...${NC}"
if ! aws ecr describe-repositories --repository-names ${ECR_REPOSITORY_NAME} --region ${AWS_REGION} 2>/dev/null; then
    echo -e "${YELLOW}📦 Creando repositorio ECR...${NC}"
    aws ecr create-repository --repository-name ${ECR_REPOSITORY_NAME} --region ${AWS_REGION}
else
    echo -e "${GREEN}✅ Repositorio ECR ya existe${NC}"
fi

# Login a ECR
echo -e "${YELLOW}🔐 Autenticando con ECR...${NC}"
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com

# Construir imagen Docker (ARM64)
echo -e "${YELLOW}🔨 Construyendo imagen Docker (ARM64)...${NC}"
docker build --platform linux/arm64 -t ${ECR_REPOSITORY_NAME} .
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Error al construir la imagen Docker${NC}"
    exit 1
fi

# Etiquetar imagen
docker tag ${ECR_REPOSITORY_NAME}:latest ${ECR_URI}:latest

# Push a ECR
echo -e "${YELLOW}📤 Subiendo imagen a ECR...${NC}"
docker push ${ECR_URI}:latest
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Error al subir la imagen a ECR${NC}"
    exit 1
fi

# Verificar si el rol IAM existe
echo -e "${YELLOW}👤 Verificando rol IAM...${NC}"
if ! aws iam get-role --role-name ${LAMBDA_ROLE_NAME} 2>/dev/null; then
    echo -e "${YELLOW}👤 Creando rol IAM...${NC}"
    
    # Crear política de confianza
    cat > trust-policy.json <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

    # Crear el rol
    aws iam create-role \
        --role-name ${LAMBDA_ROLE_NAME} \
        --assume-role-policy-document file://trust-policy.json
    
    # Adjuntar políticas necesarias
    aws iam attach-role-policy \
        --role-name ${LAMBDA_ROLE_NAME} \
        --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
    
    aws iam attach-role-policy \
        --role-name ${LAMBDA_ROLE_NAME} \
        --policy-arn arn:aws:iam::aws:policy/AmazonS3FullAccess
    
    # Limpiar archivo temporal
    rm trust-policy.json
    
    # Esperar a que el rol esté disponible
    echo -e "${YELLOW}⏳ Esperando a que el rol esté disponible...${NC}"
    sleep 10
else
    echo -e "${GREEN}✅ Rol IAM ya existe${NC}"
fi

# Obtener ARN del rol
ROLE_ARN=$(aws iam get-role --role-name ${LAMBDA_ROLE_NAME} --query 'Role.Arn' --output text)

# Verificar si la función Lambda existe
echo -e "${YELLOW}⚡ Verificando función Lambda...${NC}"
if aws lambda get-function --function-name ${LAMBDA_FUNCTION_NAME} 2>/dev/null; then
    echo -e "${YELLOW}⚡ Actualizando función Lambda existente...${NC}"
    
    # Actualizar código
    aws lambda update-function-code \
        --function-name ${LAMBDA_FUNCTION_NAME} \
        --image-uri ${ECR_URI}:latest
    
    # Esperar a que la actualización se complete
    echo -e "${YELLOW}⏳ Esperando actualización del código...${NC}"
    aws lambda wait function-updated \
        --function-name ${LAMBDA_FUNCTION_NAME}
    
    # Actualizar configuración
    aws lambda update-function-configuration \
        --function-name ${LAMBDA_FUNCTION_NAME} \
        --timeout ${LAMBDA_TIMEOUT} \
        --memory-size ${LAMBDA_MEMORY} \
        --environment "Variables={
            API_KEY=${API_KEY},
            S3_ENDPOINT_URL=${S3_ENDPOINT_URL},
            S3_ACCESS_KEY=${S3_ACCESS_KEY},
            S3_SECRET_KEY=${S3_SECRET_KEY},
            S3_REGION=${S3_REGION},
            PDF_INPUT_DIR=${PDF_INPUT_DIR}
        }"
else
    echo -e "${YELLOW}⚡ Creando nueva función Lambda...${NC}"
    
    aws lambda create-function \
        --function-name ${LAMBDA_FUNCTION_NAME} \
        --package-type Image \
        --code ImageUri=${ECR_URI}:latest \
        --role ${ROLE_ARN} \
        --timeout ${LAMBDA_TIMEOUT} \
        --memory-size ${LAMBDA_MEMORY} \
        --architectures arm64 \
        --environment "Variables={
            API_KEY=${API_KEY},
            S3_ENDPOINT_URL=${S3_ENDPOINT_URL},
            S3_ACCESS_KEY=${S3_ACCESS_KEY},
            S3_SECRET_KEY=${S3_SECRET_KEY},
            S3_REGION=${S3_REGION},
            PDF_INPUT_DIR=${PDF_INPUT_DIR}
        }"
fi

# Esperar a que la función esté activa
echo -e "${YELLOW}⏳ Esperando a que la función esté activa...${NC}"
aws lambda wait function-active --function-name ${LAMBDA_FUNCTION_NAME}

# Configurar Function URL
echo -e "${YELLOW}🌐 Configurando Function URL...${NC}"

# Verificar si ya existe una Function URL
EXISTING_URL=$(aws lambda get-function-url-config --function-name ${LAMBDA_FUNCTION_NAME} 2>/dev/null | jq -r '.FunctionUrl' || echo "")

if [ -z "$EXISTING_URL" ]; then
    # Crear Function URL
    FUNCTION_URL=$(aws lambda create-function-url-config \
        --function-name ${LAMBDA_FUNCTION_NAME} \
        --auth-type NONE \
        --cors '{
            "AllowOrigins": ["*"],
            "AllowMethods": ["POST", "OPTIONS"],
            "AllowHeaders": ["Content-Type", "x-api-key", "x-bucket-name"],
            "MaxAge": 86400
        }' \
        --query 'FunctionUrl' \
        --output text)
else
    FUNCTION_URL=$EXISTING_URL
    echo -e "${GREEN}✅ Function URL ya existe${NC}"
    
    # Actualizar configuración CORS
    aws lambda update-function-url-config \
        --function-name ${LAMBDA_FUNCTION_NAME} \
        --auth-type NONE \
        --cors '{
            "AllowOrigins": ["*"],
            "AllowMethods": ["POST", "OPTIONS"],
            "AllowHeaders": ["Content-Type", "x-api-key", "x-bucket-name"],
            "MaxAge": 86400
        }'
fi

# Agregar permisos para Function URL si es necesario
echo -e "${YELLOW}🔓 Configurando permisos de Function URL...${NC}"
aws lambda add-permission \
    --function-name ${LAMBDA_FUNCTION_NAME} \
    --statement-id FunctionURLAllowPublicAccess \
    --action lambda:InvokeFunctionUrl \
    --principal "*" \
    --function-url-auth-type NONE 2>/dev/null || echo -e "${GREEN}✅ Permisos ya configurados${NC}"

echo -e "${GREEN}✅ Despliegue completado exitosamente!${NC}"
echo -e "${GREEN}📋 Resumen:${NC}"
echo -e "  - Función Lambda: ${LAMBDA_FUNCTION_NAME}"
echo -e "  - Región: ${AWS_REGION}"
echo -e "  - Arquitectura: ARM64"
echo -e "  - Memoria: ${LAMBDA_MEMORY} MB"
echo -e "  - Timeout: ${LAMBDA_TIMEOUT} segundos"
echo -e "  - Function URL: ${FUNCTION_URL}"
echo -e ""
echo -e "${YELLOW}⚠️  Importante:${NC}"
echo -e "  - Actualiza las credenciales S3 (S3_ACCESS_KEY y S3_SECRET_KEY)"
echo -e "  - La API Key configurada es: ${API_KEY}"
echo -e "  - Directorios S3 configurados:"
echo -e "    - PDFs de entrada: ${PDF_INPUT_DIR}/" 