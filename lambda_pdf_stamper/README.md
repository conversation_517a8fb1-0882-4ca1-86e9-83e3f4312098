# Lambda PDF Stamper

Función AWS Lambda para estampar documentos PDF con imágenes, sobrescribiendo el archivo original en el mismo directorio de S3.

## Características

- ✅ Estampado de PDFs con imágenes PNG
- ✅ Posicionamiento preciso (x, y, width, height)
- ✅ Soporte para rotación de estampillas
- ✅ Sobrescritura del archivo original
- ✅ Compatible con S3 y object storage S3-compatible
- ✅ Autenticación mediante API Key
- ✅ URLs prefirmadas para descarga
- ✅ Arquitectura ARM64 optimizada

## Comportamiento

**⚠️ Importante**: Esta lambda **sobrescribe el archivo PDF original** con la versión estampada. No se mantiene backup del archivo original.

## Configuración

### Variables de Entorno

```bash
API_KEY=PDFStamper2024SecureKey
S3_ENDPOINT_URL=https://s3.amazonaws.com
S3_ACCESS_KEY=tu_access_key
S3_SECRET_KEY=tu_secret_key
S3_REGION=us-east-1
PDF_INPUT_DIR=documentos
STAMP_INPUT_DIR=radicados
```

### Estructura de Directorios en S3

```
bucket/
├── documentos/           # PDFs de entrada (se sobrescriben)
│   ├── documento1.pdf
│   └── documento2.pdf
└── radicados/           # Estampillas PNG
    ├── estampilla1.png
    └── estampilla2.png
```

## Uso

### Function URL (Recomendado)

**Endpoint**: `https://h7uu5sgi6txwr3bfotbye3bfotbye3thai0tfkes.lambda-url.us-east-1.on.aws/`

**Headers requeridos**:
```
x-api-key: PDFStamper2024SecureKey
x-bucket-name: nombre-del-bucket
Content-Type: application/json
```

**Body**:
```json
{
  "properties": {
    "page": 1,
    "x": 100,
    "y": 100,
    "width": 200,
    "height": 100,
    "rotated": false
  },
  "pdf_path": "documento.pdf",
  "stamp_path": "estampilla.png",
  "expiration_time": 3600
}
```

### Ejemplo con curl

```bash
curl -X POST \
  https://h7uu5sgi6txwr3bfotbye3bfotbye3thai0tfkes.lambda-url.us-east-1.on.aws/ \
  -H "x-api-key: PDFStamper2024SecureKey" \
  -H "x-bucket-name: mi-bucket" \
  -H "Content-Type: application/json" \
  -d '{
    "properties": {
      "page": 1,
      "x": 50,
      "y": 50,
      "width": 150,
      "height": 75,
      "rotated": false
    },
    "pdf_path": "contrato.pdf",
    "stamp_path": "sello-aprobado.png",
    "expiration_time": 3600
  }'
```

### Respuesta Exitosa

```json
{
  "statusCode": 200,
  "message": "PDF estampado exitosamente",
  "data": {
    "filename": "contrato.pdf",
    "s3_key": "documentos/contrato.pdf",
    "bucket": "mi-bucket",
    "content_type": "application/pdf",
    "page_stamped": 1,
    "stamp_position": {
      "x": 50,
      "y": 50,
      "width": 150,
      "height": 75,
      "rotated": false
    },
    "expiration_time": 3600,
    "presigned_url": "https://..."
  }
}
```

## Parámetros

### Properties (Requerido)

| Campo | Tipo | Descripción |
|-------|------|-------------|
| `page` | int | Número de página (1-indexed) |
| `x` | float | Posición X en puntos |
| `y` | float | Posición Y en puntos |
| `width` | float | Ancho de la estampilla |
| `height` | float | Alto de la estampilla |
| `rotated` | bool | Rotar estampilla 90° (opcional) |

### Otros Parámetros

| Campo | Tipo | Descripción |
|-------|------|-------------|
| `pdf_path` | string | Nombre del archivo PDF en S3 |
| `stamp_path` | string | Nombre de la estampilla PNG en S3 |
| `expiration_time` | int | Segundos para URL prefirmada (0 = sin URL) |

## Deployment

```bash
# Configurar credenciales AWS
aws configure

# Ejecutar deploy
./deploy.sh
```

## Actualización de Variables de Entorno

### Comando AWS CLI para actualizar todas las variables

```bash
aws lambda update-function-configuration \
  --function-name pdf-stamper-lambda \
  --environment "Variables={
    API_KEY=PDFStamper2024SecureKey,
    S3_ENDPOINT_URL=https://s3.amazonaws.com,
    S3_ACCESS_KEY=tu_access_key_real,
    S3_SECRET_KEY=tu_secret_key_real,
    S3_REGION=us-east-1,
    PDF_INPUT_DIR=documentos,
    STAMP_INPUT_DIR=radicados
  }"
```

### Comandos individuales para variables específicas

```bash
# Actualizar solo las credenciales S3
aws lambda update-function-configuration \
  --function-name pdf-stamper-lambda \
  --environment "Variables={
    API_KEY=PDFStamper2024SecureKey,
    S3_ENDPOINT_URL=https://s3.amazonaws.com,
    S3_ACCESS_KEY=AKIAIOSFODNN7EXAMPLE,
    S3_SECRET_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY,
    S3_REGION=us-east-1,
    PDF_INPUT_DIR=documentos,
    STAMP_INPUT_DIR=radicados
  }"

# Cambiar API Key de seguridad
aws lambda update-function-configuration \
  --function-name pdf-stamper-lambda \
  --environment "Variables={
    API_KEY=NuevoAPIKey2024Seguro,
    S3_ENDPOINT_URL=https://s3.amazonaws.com,
    S3_ACCESS_KEY=tu_access_key,
    S3_SECRET_KEY=tu_secret_key,
    S3_REGION=us-east-1,
    PDF_INPUT_DIR=documentos,
    STAMP_INPUT_DIR=radicados
  }"

# Cambiar directorios S3
aws lambda update-function-configuration \
  --function-name pdf-stamper-lambda \
  --environment "Variables={
    API_KEY=PDFStamper2024SecureKey,
    S3_ENDPOINT_URL=https://s3.amazonaws.com,
    S3_ACCESS_KEY=tu_access_key,
    S3_SECRET_KEY=tu_secret_key,
    S3_REGION=us-east-1,
    PDF_INPUT_DIR=pdfs-entrada,
    STAMP_INPUT_DIR=sellos
  }"
```

### Verificar variables actuales

```bash
# Ver todas las variables de entorno configuradas
aws lambda get-function-configuration \
  --function-name pdf-stamper-lambda \
  --query 'Environment.Variables' \
  --output table

# Ver solo las variables en formato JSON
aws lambda get-function-configuration \
  --function-name pdf-stamper-lambda \
  --query 'Environment.Variables'
```

### Notas importantes

- ⚠️ **Siempre incluye todas las variables** en el comando `update-function-configuration`, ya que sobrescribe completamente la configuración existente
- 🔒 **Nunca expongas credenciales reales** en scripts o documentación
- ⏱️ Los cambios pueden tardar unos segundos en aplicarse
- 📝 Verifica la configuración después de cada actualización

## Arquitectura

- **Runtime**: Python 3.11 en contenedor Docker
- **Arquitectura**: ARM64 (Graviton2)
- **Memoria**: 1024 MB
- **Timeout**: 300 segundos
- **Storage**: /tmp (512 MB)

## Seguridad

- Autenticación mediante API Key en headers
- Validación de parámetros de entrada
- Limpieza automática de archivos temporales
- CORS configurado para acceso web

## Limitaciones

- ⚠️ **El archivo PDF original se sobrescribe** - no hay backup
- Máximo 512 MB de storage temporal
- Timeout de 5 minutos por ejecución
- Solo soporta estampillas PNG
- Tamaño máximo de archivo limitado por Lambda

## Troubleshooting

### Error 401: API key inválido
- Verificar header `x-api-key`
- Confirmar valor en variables de entorno

### Error 400: Bucket name faltante
- Verificar header `x-bucket-name`

### Error 404: Archivo no encontrado
- Verificar que el archivo existe en S3
- Confirmar paths y nombres de archivo

### Error 500: Error interno
- Revisar logs de CloudWatch
- Verificar credenciales S3
- Confirmar conectividad con S3

## Logs

Los logs están disponibles en CloudWatch Logs:
- Grupo: `/aws/lambda/pdf-stamper-lambda`
- Retención: 14 días (configurable) 