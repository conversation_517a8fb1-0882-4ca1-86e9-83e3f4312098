#!/usr/bin/env python3
"""
Ejemplo de uso de la funcionalidad de PDF en blanco
"""

import json

def ejemplo_request_pdf_blanco():
    """
    Ejemplo de request para generar y estampar un PDF en blanco
    """
    
    # Ejemplo para Function URL (HTTP request)
    request_http = {
        "httpMethod": "POST",
        "headers": {
            "x-api-key": "PDFStamper2024SecureKey",
            "x-bucket-name": "mi-bucket-s3",
            "Content-Type": "application/json"
        },
        "body": json.dumps({
            "properties": {
                "x": 100,
                "y": 150,
                "width": 200,
                "height": 80,
                "rotated": False
            },
            "pdf_path": "",  # String vacío para generar PDF en blanco
            "stamp_path": "estampillas/sello_oficial.png",
            "expiration_time": 3600
        })
    }
    
    # Ejemplo para Lambda tradicional
    request_lambda = {
        "properties": {
            "x": 100,
            "y": 150,
            "width": 200,
            "height": 80,
            "rotated": False
        },
        "pdf_path": "",  # String vacío para generar PDF en blanco
        "stamp_path": "estampillas/sello_oficial.png",
        "bucket_name": "mi-bucket-s3",
        "expiration_time": 3600,
        "api_key": "PDFStamper2024SecureKey"
    }
    
    print("=== Ejemplo de Request HTTP (Function URL) ===")
    print(json.dumps(request_http, indent=2, ensure_ascii=False))
    
    print("\n=== Ejemplo de Request Lambda Tradicional ===")
    print(json.dumps(request_lambda, indent=2, ensure_ascii=False))
    
    return request_http, request_lambda

def ejemplo_response_pdf_blanco():
    """
    Ejemplo de respuesta esperada para PDF en blanco
    """
    
    response_exitosa = {
        "statusCode": 200,
        "message": "PDF estampado exitosamente",
        "data": {
            "filename": "documento_temporal_20250704_121500.pdf",
            "s3_key": "temporales/documento_temporal_20250704_121500.pdf",
            "bucket": "mi-bucket-s3",
            "content_type": "application/pdf",
            "page_stamped": 1,
            "is_blank_pdf": True,
            "stamp_position": {
                "x": 100,
                "y": 150,
                "width": 200,
                "height": 80,
                "rotated": False
            },
            "expiration_time": 3600,
            "presigned_url": "https://mi-bucket-s3.s3.amazonaws.com/temporales/documento_temporal_20250704_121500.pdf?..."
        }
    }
    
    print("=== Ejemplo de Respuesta Exitosa ===")
    print(json.dumps(response_exitosa, indent=2, ensure_ascii=False))
    
    return response_exitosa

def ejemplo_curl_pdf_blanco():
    """
    Ejemplo de comando curl para probar la funcionalidad
    """
    
    curl_command = '''
# Ejemplo de curl para generar PDF en blanco y estamparlo
curl -X POST "https://tu-lambda-url.lambda-url.region.on.aws/" \\
  -H "Content-Type: application/json" \\
  -H "x-api-key: PDFStamper2024SecureKey" \\
  -H "x-bucket-name: mi-bucket-s3" \\
  -d '{
    "properties": {
      "x": 100,
      "y": 150,
      "width": 200,
      "height": 80,
      "rotated": false
    },
    "pdf_path": "",
    "stamp_path": "estampillas/sello_oficial.png",
    "expiration_time": 3600
  }'
'''
    
    print("=== Ejemplo de comando curl ===")
    print(curl_command)
    
    return curl_command

def diferencias_con_flujo_normal():
    """
    Explica las diferencias entre el flujo normal y el de PDF en blanco
    """
    
    print("=== Diferencias entre flujo normal y PDF en blanco ===")
    print()
    print("FLUJO NORMAL:")
    print("- pdf_path: 'ruta/al/documento.pdf' (ruta válida)")
    print("- properties.page: requerido (número de página a estampar)")
    print("- Descarga PDF existente desde S3")
    print("- Estampa en la página especificada")
    print("- Sobrescribe el archivo original en S3")
    print()
    print("FLUJO PDF EN BLANCO:")
    print("- pdf_path: '' (string vacío)")
    print("- properties.page: ignorado (siempre estampa en página 1)")
    print("- Genera PDF en blanco de una página")
    print("- Estampa en la única página disponible")
    print("- Guarda en directorio 'temporales/' de S3")
    print("- Respuesta incluye 'is_blank_pdf': true")

def main():
    """
    Ejecuta todos los ejemplos
    """
    print("Ejemplos de uso - Funcionalidad PDF en blanco")
    print("=" * 60)
    
    ejemplo_request_pdf_blanco()
    print("\n" + "=" * 60)
    
    ejemplo_response_pdf_blanco()
    print("\n" + "=" * 60)
    
    ejemplo_curl_pdf_blanco()
    print("\n" + "=" * 60)
    
    diferencias_con_flujo_normal()

if __name__ == "__main__":
    main()
