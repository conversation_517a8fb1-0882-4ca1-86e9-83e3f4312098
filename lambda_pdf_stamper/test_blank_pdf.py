#!/usr/bin/env python3
"""
Script de prueba para validar la funcionalidad de PDF en blanco
"""

import json
import os
import sys
import tempfile
from datetime import datetime

# Agregar el directorio actual al path para importar los módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pdf_stamper import PDFStamper
from lambda_function import stamp_pdf_service, validate_stamp_properties

def test_blank_pdf_creation():
    """Prueba la creación de PDF en blanco"""
    print("=== Prueba: Creación de PDF en blanco ===")
    
    try:
        stamper = PDFStamper(output_directory="/tmp")
        pdf_path = stamper.create_blank_pdf()
        
        if os.path.exists(pdf_path):
            print(f"✓ PDF en blanco creado exitosamente: {pdf_path}")
            
            # Verificar información del PDF
            info = stamper.get_pdf_info(pdf_path)
            print(f"✓ Páginas: {info['page_count']}")
            print(f"✓ Dimensiones: {info['pages'][0]['width_pts']}x{info['pages'][0]['height_pts']} pts")
            
            # Limpiar
            os.unlink(pdf_path)
            return True
        else:
            print("✗ Error: PDF no fue creado")
            return False
            
    except Exception as e:
        print(f"✗ Error en creación de PDF: {e}")
        return False

def test_validation_blank_pdf():
    """Prueba las validaciones para PDF en blanco"""
    print("\n=== Prueba: Validaciones para PDF en blanco ===")
    
    try:
        # Propiedades válidas para PDF en blanco (sin 'page')
        properties_blank = {
            'x': 100,
            'y': 100,
            'width': 200,
            'height': 100
        }
        
        validate_stamp_properties(properties_blank, is_blank_pdf=True)
        print("✓ Validación exitosa para PDF en blanco")
        
        # Propiedades válidas para PDF normal (con 'page')
        properties_normal = {
            'page': 1,
            'x': 100,
            'y': 100,
            'width': 200,
            'height': 100
        }
        
        validate_stamp_properties(properties_normal, is_blank_pdf=False)
        print("✓ Validación exitosa para PDF normal")
        
        return True
        
    except Exception as e:
        print(f"✗ Error en validaciones: {e}")
        return False

def test_validation_errors():
    """Prueba casos de error en validaciones"""
    print("\n=== Prueba: Casos de error en validaciones ===")
    
    try:
        # Caso 1: PDF normal sin 'page'
        properties_invalid = {
            'x': 100,
            'y': 100,
            'width': 200,
            'height': 100
        }
        
        try:
            validate_stamp_properties(properties_invalid, is_blank_pdf=False)
            print("✗ Error: Debería fallar validación sin 'page'")
            return False
        except ValueError as e:
            print(f"✓ Error esperado capturado: {e}")
        
        # Caso 2: Dimensiones inválidas
        properties_invalid2 = {
            'x': 100,
            'y': 100,
            'width': -200,
            'height': 100
        }
        
        try:
            validate_stamp_properties(properties_invalid2, is_blank_pdf=True)
            print("✗ Error: Debería fallar con dimensiones negativas")
            return False
        except ValueError as e:
            print(f"✓ Error esperado capturado: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error inesperado: {e}")
        return False

def create_test_stamp():
    """Crea una imagen de estampilla de prueba"""
    from PIL import Image, ImageDraw
    
    # Crear imagen simple de 100x50 píxeles
    img = Image.new('RGBA', (100, 50), (255, 0, 0, 128))  # Rojo semi-transparente
    draw = ImageDraw.Draw(img)
    draw.text((10, 20), "TEST", fill=(255, 255, 255, 255))
    
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
    img.save(temp_file.name, 'PNG')
    temp_file.close()
    
    return temp_file.name

def test_stamping_blank_pdf():
    """Prueba el estampado en PDF en blanco"""
    print("\n=== Prueba: Estampado en PDF en blanco ===")
    
    try:
        stamper = PDFStamper(output_directory="/tmp")
        
        # Crear PDF en blanco
        blank_pdf = stamper.create_blank_pdf()
        print(f"✓ PDF en blanco creado: {blank_pdf}")
        
        # Crear estampilla de prueba
        stamp_path = create_test_stamp()
        print(f"✓ Estampilla de prueba creada: {stamp_path}")
        
        # Realizar estampado
        stamped_pdf = stamper.stamp_pdf(
            pdf_path=blank_pdf,
            stamp_image_path=stamp_path,
            x=50,
            y=50,
            width=100,
            height=50,
            page_number=1,
            output_filename="test_stamped_blank.pdf"
        )
        
        if os.path.exists(stamped_pdf):
            print(f"✓ PDF estampado creado exitosamente: {stamped_pdf}")
            
            # Verificar que el PDF estampado tiene contenido
            info = stamper.get_pdf_info(stamped_pdf)
            print(f"✓ PDF estampado tiene {info['page_count']} página(s)")
            
            # Limpiar archivos
            os.unlink(blank_pdf)
            os.unlink(stamp_path)
            os.unlink(stamped_pdf)
            
            return True
        else:
            print("✗ Error: PDF estampado no fue creado")
            return False
            
    except Exception as e:
        print(f"✗ Error en estampado: {e}")
        return False

def main():
    """Ejecuta todas las pruebas"""
    print("Iniciando pruebas de funcionalidad PDF en blanco...")
    print("=" * 60)
    
    tests = [
        test_blank_pdf_creation,
        test_validation_blank_pdf,
        test_validation_errors,
        test_stamping_blank_pdf
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Resultados: {passed}/{total} pruebas exitosas")
    
    if passed == total:
        print("✓ Todas las pruebas pasaron correctamente")
        return 0
    else:
        print("✗ Algunas pruebas fallaron")
        return 1

if __name__ == "__main__":
    sys.exit(main())
