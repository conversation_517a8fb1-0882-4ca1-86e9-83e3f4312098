#!/bin/bash

# Configuración real de la Lambda desplegada
FUNCTION_URL="https://h7uu5sgi6txwr3bfotbye3thai0tfkes.lambda-url.us-east-1.on.aws/"
API_KEY="PDFStamper2024SecureKey"
BUCKET_NAME="test-bucket"

echo "=== Pruebas para Lambda PDF Stamper ==="
echo "Function URL: $FUNCTION_URL"
echo ""

echo "1. Prueba básica - Estampar PDF con URL de descarga:"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $API_KEY" \
  -H "x-bucket-name: $BUCKET_NAME" \
  -d '{
    "properties": {
        "page": 1,
        "x": 400,
        "y": 50,
        "width": 150,
        "height": 75,
        "rotated": false
    },
    "pdf_path": "documento_prueba.pdf",
    "stamp_path": "estampilla_radicado.png",
    "expiration_time": 3600
  }'

echo ""
echo ""
echo "2. Prueba sin URL de descarga (expiration_time = 0):"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $API_KEY" \
  -H "x-bucket-name: $BUCKET_NAME" \
  -d '{
    "properties": {
        "page": 1,
        "x": 100,
        "y": 100,
        "width": 200,
        "height": 100,
        "rotated": false
    },
    "pdf_path": "documento_prueba.pdf",
    "stamp_path": "estampilla_radicado.png",
    "expiration_time": 0
  }'

echo ""
echo ""
echo "3. Prueba de error - Sin autenticación:"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-bucket-name: $BUCKET_NAME" \
  -d '{
    "properties": {
        "page": 1,
        "x": 100,
        "y": 100,
        "width": 200,
        "height": 100,
        "rotated": false
    },
    "pdf_path": "documento.pdf",
    "stamp_path": "estampilla.png",
    "expiration_time": 3600
  }'

echo ""
echo ""
echo "4. Prueba de error - Parámetros faltantes:"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $API_KEY" \
  -H "x-bucket-name: $BUCKET_NAME" \
  -d '{
    "properties": {
        "page": 1,
        "x": 100,
        "y": 100
    },
    "pdf_path": "documento.pdf",
    "stamp_path": "estampilla.png",
    "expiration_time": 3600
  }'

echo ""
echo ""
echo "=== Información de la Lambda ==="
echo "- Función: pdf-stamper-lambda"
echo "- Región: us-east-1"
echo "- URL: $FUNCTION_URL"
echo "- API Key: $API_KEY"
echo ""
echo "=== Estructura S3 esperada ==="
echo "bucket/"
echo "├── documentos/              # PDFs de entrada"
echo "├── radicados/              # Estampillas PNG"
echo "└── documentos-estampados/  # PDFs de salida"
echo ""
echo "⚠️  Nota: Actualiza las credenciales S3 en las variables de entorno de la Lambda"
echo ""
echo "=== Comandos cURL individuales ==="
echo ""
echo "Comando básico:"
cat << 'EOF'
curl -X POST https://h7uu5sgi6txwr3bfotbye3thai0tfkes.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: PDFStamper2024SecureKey" \
  -H "x-bucket-name: tu-bucket" \
  -d '{
    "properties": {
        "page": 1,
        "x": 400,
        "y": 50,
        "width": 150,
        "height": 75,
        "rotated": false
    },
    "pdf_path": "documento.pdf",
    "stamp_path": "estampilla.png",
    "expiration_time": 3600
  }'
EOF 