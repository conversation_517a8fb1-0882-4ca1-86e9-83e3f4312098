import json
import os
import tempfile
from typing import Dict, Any, Optional
from datetime import datetime
import boto3
from botocore.config import Config
from botocore.exceptions import ClientError

# Importar la implementación de PDFStamper
from pdf_stamper import PDFStamper

class S3PDFClient:
    def __init__(self):
        # Configurar path-style addressing para object storage S3-compatible
        config = Config(
            s3={
                'addressing_style': 'path'
            }
        )
        
        # Configuración de cliente S3
        self.s3_client = boto3.client(
            's3',
            endpoint_url=os.getenv('S3_ENDPOINT_URL'),
            aws_access_key_id=os.getenv('S3_ACCESS_KEY'),
            aws_secret_access_key=os.getenv('S3_SECRET_KEY'),
            region_name=os.getenv('S3_REGION', 'us-east-1'),
            config=config
        )
    
    def download_file(self, bucket_name: str, s3_key: str, file_suffix: str) -> Optional[str]:
        """
        Descarga un archivo desde S3 a un archivo temporal
        
        Args:
            bucket_name: Nombre del bucket
            s3_key: Clave del objeto en S3
            file_suffix: Sufijo para el archivo temporal (.pdf o .png)
            
        Returns:
            Path del archivo temporal o None si hay error
        """
        try:
            # Crear archivo temporal
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_suffix)
            temp_file.close()
            
            # Descargar desde S3
            self.s3_client.download_file(bucket_name, s3_key, temp_file.name)
            
            print(f"Archivo descargado exitosamente desde S3: {s3_key}")
            return temp_file.name
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                print(f"Archivo no encontrado en S3: {s3_key}")
            else:
                print(f"Error de cliente S3 al descargar archivo: {e}")
            return None
        except Exception as e:
            print(f"Error inesperado al descargar archivo: {e}")
            return None
    
    def upload_pdf(self, bucket_name: str, pdf_path: str, original_s3_key: str) -> str:
        """
        Sube un PDF estampado a S3 sobrescribiendo el archivo original

        Args:
            bucket_name: Nombre del bucket
            pdf_path: Path local del PDF
            original_s3_key: Clave S3 del archivo original para sobrescribir

        Returns:
            S3 key del archivo subido (mismo que el original)
        """
        try:
            # Subir a S3 sobrescribiendo el archivo original
            self.s3_client.upload_file(
                pdf_path,
                bucket_name,
                original_s3_key,
                ExtraArgs={'ContentType': 'application/pdf'}
            )

            print(f"PDF estampado subido exitosamente sobrescribiendo: {original_s3_key}")
            return original_s3_key

        except ClientError as e:
            print(f"Error de cliente S3 al subir PDF: {e}")
            raise ValueError(f"Error al subir PDF estampado a S3: {str(e)}")
        except Exception as e:
            print(f"Error inesperado al subir PDF: {e}")
            raise ValueError(f"Error interno al subir PDF: {str(e)}")

    def upload_temporary_pdf(self, bucket_name: str, pdf_path: str, s3_key: str) -> str:
        """
        Sube un PDF temporal a S3 en el directorio temporales/

        Args:
            bucket_name: Nombre del bucket
            pdf_path: Path local del PDF
            s3_key: Clave S3 donde guardar el archivo

        Returns:
            S3 key del archivo subido
        """
        try:
            # Subir a S3 en directorio temporales
            self.s3_client.upload_file(
                pdf_path,
                bucket_name,
                s3_key,
                ExtraArgs={'ContentType': 'application/pdf'}
            )

            print(f"PDF temporal subido exitosamente: {s3_key}")
            return s3_key

        except ClientError as e:
            print(f"Error de cliente S3 al subir PDF temporal: {e}")
            raise ValueError(f"Error al subir PDF temporal a S3: {str(e)}")
        except Exception as e:
            print(f"Error inesperado al subir PDF temporal: {e}")
            raise ValueError(f"Error interno al subir PDF temporal: {str(e)}")
    
    def generate_presigned_url(self, bucket_name: str, s3_key: str, expiration_seconds: int) -> Optional[str]:
        """
        Genera URL prefirmada para descarga
        
        Args:
            bucket_name: Nombre del bucket
            s3_key: Clave del objeto en S3
            expiration_seconds: Tiempo de expiración en segundos
            
        Returns:
            URL prefirmada o None si hay error
        """
        try:
            if expiration_seconds <= 0:
                return None
                
            url = self.s3_client.generate_presigned_url(
                ClientMethod='get_object',
                Params={
                    'Bucket': bucket_name,
                    'Key': s3_key
                },
                ExpiresIn=expiration_seconds
            )
            
            return url
            
        except ClientError as e:
            print(f"Error de cliente S3 al generar URL prefirmada: {e}")
            return None
        except Exception as e:
            print(f"Error inesperado al generar URL prefirmada: {e}")
            return None

def authenticate_request(headers: Dict[str, str]) -> bool:
    """
    Valida el API key desde los headers
    
    Args:
        headers: Headers de la request
        
    Returns:
        True si la autenticación es exitosa
    """
    api_key = os.getenv('API_KEY', 'PDFStamper2024SecureKey')
    
    # Buscar el header x-api-key (case insensitive)
    request_api_key = None
    for key, value in headers.items():
        if key.lower() == 'x-api-key':
            request_api_key = value
            break
    
    if not request_api_key:
        return False
    
    return request_api_key == api_key

def parse_event(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parsea el evento para soportar tanto Lambda tradicional como Function URL
    Extrae bucket_name desde headers para mayor seguridad
    """
    # Detectar si es un evento de Function URL (HTTP request)
    if 'httpMethod' in event or 'requestContext' in event:
        # Es un evento de Function URL
        headers = event.get('headers', {})
        
        # Autenticar la request
        if not authenticate_request(headers):
            raise ValueError("API key inválido o faltante")
        
        # Extraer bucket_name desde headers
        bucket_name = None
        for key, value in headers.items():
            if key.lower() == 'x-bucket-name':
                bucket_name = value
                break
        
        if not bucket_name:
            raise ValueError("Header 'x-bucket-name' requerido")
        
        # Parsear body para otros parámetros
        parsed_data = {'bucket_name': bucket_name}
        
        try:
            if 'body' in event and event['body']:
                # El body puede venir como string, parsearlo
                if isinstance(event['body'], str):
                    body = json.loads(event['body'])
                else:
                    body = event['body']
                parsed_data.update(body)
            else:
                # Si no hay body, buscar en queryStringParameters
                query_params = event.get('queryStringParameters', {})
                if query_params:
                    parsed_data.update(query_params)
        except json.JSONDecodeError:
            raise ValueError("Body JSON inválido")
            
        return parsed_data
    else:
        # Es un evento de Lambda tradicional
        # Para backward compatibility, usar API key desde event si está presente
        if 'api_key' in event:
            api_key = os.getenv('API_KEY', 'PDFStamper2024SecureKey')
            if event['api_key'] != api_key:
                raise ValueError("API key inválido")
        
        return event

def validate_stamp_properties(properties: Dict[str, Any], is_blank_pdf: bool = False) -> None:
    """
    Valida las propiedades de estampado

    Args:
        properties: Diccionario con las propiedades de estampado
        is_blank_pdf: Si es True, ignora la validación del campo 'page'
    """
    if is_blank_pdf:
        required_fields = ['x', 'y', 'width', 'height']
    else:
        required_fields = ['page', 'x', 'y', 'width', 'height']

    missing_fields = [field for field in required_fields if field not in properties]

    if missing_fields:
        raise ValueError(f"Campos faltantes en properties: {', '.join(missing_fields)}")

    # Validar tipos y valores
    if not is_blank_pdf:
        if not isinstance(properties['page'], int) or properties['page'] < 1:
            raise ValueError("'page' debe ser un entero mayor a 0")

    for field in ['x', 'y', 'width', 'height']:
        if not isinstance(properties[field], (int, float)):
            raise ValueError(f"'{field}' debe ser un número")

    if properties['width'] <= 0 or properties['height'] <= 0:
        raise ValueError("'width' y 'height' deben ser valores positivos")

def stamp_pdf_service(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Servicio principal para estampar PDFs
    
    Args:
        request_data: Datos de la request parseados
        
    Returns:
        Dict con la respuesta de éxito o error
    """
    # Validar parámetros requeridos
    required_params = ['properties', 'pdf_path', 'stamp_path', 'bucket_name', 'expiration_time']
    missing_params = [param for param in required_params if param not in request_data]
    
    if missing_params:
        raise ValueError(f'Parámetros faltantes: {", ".join(missing_params)}')
    
    # Extraer parámetros
    properties = request_data['properties']
    pdf_path = request_data['pdf_path']
    stamp_path = request_data['stamp_path']
    bucket_name = request_data['bucket_name']
    expiration_time = request_data['expiration_time']

    # Determinar si es PDF en blanco
    is_blank_pdf = pdf_path == ""

    # Validar properties (ignorar 'page' si es PDF en blanco)
    validate_stamp_properties(properties, is_blank_pdf)
    
    # Validar expiration_time
    if not isinstance(expiration_time, int) or expiration_time < 0:
        raise ValueError("'expiration_time' debe ser un entero >= 0")
    
    # Inicializar cliente S3 y PDFStamper
    s3_client = S3PDFClient()
    stamper = PDFStamper(output_directory="/tmp")
    
    # Archivos temporales para limpiar
    temp_pdf_file = None
    temp_stamp_file = None
    stamped_pdf_path = None
    
    try:
        # Manejar caso de PDF en blanco cuando pdf_path está vacío
        if pdf_path == "":
            print("Generando PDF en blanco para estampado")
            temp_pdf_file = stamper.create_blank_pdf()
            original_filename = f"documento_temporal_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            pdf_s3_key = f"temporales/{original_filename}"
        else:
            # Flujo normal: descargar PDF existente desde S3
            pdf_input_dir = os.getenv('PDF_INPUT_DIR', 'documentos')
            pdf_s3_key = f"{pdf_input_dir}/{pdf_path}"

            print(f"Descargando PDF desde S3: {pdf_s3_key}")
            temp_pdf_file = s3_client.download_file(bucket_name, pdf_s3_key, '.pdf')
            if not temp_pdf_file:
                raise ValueError(f"No se pudo descargar el PDF: {pdf_path}")

            original_filename = os.path.basename(pdf_path)

        # Descargar estampilla desde S3
        stamp_s3_key = stamp_path  # Usar directamente el path completo proporcionado
        print(f"Descargando estampilla desde S3: {stamp_s3_key}")
        temp_stamp_file = s3_client.download_file(bucket_name, stamp_s3_key, '.png')
        if not temp_stamp_file:
            raise ValueError(f"No se pudo descargar la estampilla: {stamp_path}")
        
        # Realizar el estampado
        if is_blank_pdf:
            print(f"Estampando PDF en blanco: posición ({properties['x']}, {properties['y']})")
            page_number = 1  # PDF en blanco siempre tiene una página
        else:
            print(f"Estampando PDF: página {properties['page']}, posición ({properties['x']}, {properties['y']})")
            page_number = properties['page']

        stamped_pdf_path = stamper.stamp_pdf(
            pdf_path=temp_pdf_file,
            stamp_image_path=temp_stamp_file,
            x=properties['x'],
            y=properties['y'],
            width=properties['width'],
            height=properties['height'],
            page_number=page_number,
            output_filename=original_filename,
            rotate=properties.get('rotated', False)
        )

        # Subir PDF estampado a S3
        if is_blank_pdf:
            s3_key = s3_client.upload_temporary_pdf(bucket_name, stamped_pdf_path, pdf_s3_key)
        else:
            s3_key = s3_client.upload_pdf(bucket_name, stamped_pdf_path, pdf_s3_key)
        
        # Generar URL prefirmada si se solicita
        presigned_url = None
        if expiration_time > 0:
            presigned_url = s3_client.generate_presigned_url(
                bucket_name,
                s3_key,
                expiration_time
            )
        
        # Preparar respuesta
        response_data = {
            'filename': original_filename,
            's3_key': s3_key,
            'bucket': bucket_name,
            'content_type': 'application/pdf',
            'page_stamped': page_number,
            'is_blank_pdf': is_blank_pdf,
            'stamp_position': {
                'x': properties['x'],
                'y': properties['y'],
                'width': properties['width'],
                'height': properties['height'],
                'rotated': properties.get('rotated', False)
            },
            'expiration_time': expiration_time
        }
        
        # Agregar URL prefirmada solo si se generó
        if presigned_url:
            response_data['presigned_url'] = presigned_url
        
        return response_data
        
    finally:
        # Limpiar archivos temporales
        for temp_file in [temp_pdf_file, temp_stamp_file, stamped_pdf_path]:
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                    print(f"Archivo temporal eliminado: {temp_file}")
                except Exception as e:
                    print(f"No se pudo eliminar archivo temporal {temp_file}: {e}")

def lambda_handler(event: Dict[str, Any], context) -> Dict[str, Any]:
    """
    Handler principal que soporta tanto eventos Lambda como Function URL
    
    Para Function URL:
    Headers requeridos:
    - x-api-key: Clave de autenticación
    - x-bucket-name: Nombre del bucket S3
    
    Body:
    {
        "properties": {
            "page": 1,
            "x": 100,
            "y": 100,
            "width": 200,
            "height": 100,
            "rotated": false
        },
        "pdf_path": "documento.pdf",
        "stamp_path": "estampilla.png",
        "expiration_time": 3600
    }
    
    Para Lambda tradicional:
    {
        "properties": {...},
        "pdf_path": "documento.pdf",
        "stamp_path": "estampilla.png",
        "bucket_name": "nombre-del-bucket",
        "expiration_time": 3600,
        "api_key": "clave-opcional-para-validacion"
    }
    """
    try:
        # Parsear el evento (detecta automáticamente el formato y valida autenticación)
        parsed_event = parse_event(event)
        
        # Estampar PDF y obtener resultado
        result = stamp_pdf_service(parsed_event)
        
        success_response = {
            'statusCode': 200,
            'message': 'PDF estampado exitosamente',
            'data': result
        }
        
        # Formato de respuesta según el tipo de evento
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, x-api-key, x-bucket-name'
                },
                'body': json.dumps(success_response)
            }
        else:
            return success_response
            
    except ValueError as e:
        # Errores de validación (autenticación, parámetros, etc.)
        error_response = {
            'error': str(e),
            'security_hint': 'Verifica headers: x-api-key, x-bucket-name',
            'required_params': ['properties', 'pdf_path', 'stamp_path', 'expiration_time']
        }
        
        status_code = 401 if 'API key' in str(e) else 400
        
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': status_code,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps(error_response)
            }
        else:
            return {
                'statusCode': status_code,
                'body': json.dumps(error_response)
            }
            
    except Exception as e:
        print(f"Error en lambda_handler: {e}")
        error_response = {
            'error': f'Error interno del servidor: {str(e)}',
            'hint': 'Contacta al administrador si el problema persiste'
        }
        
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': 500,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps(error_response)
            }
        else:
            return {
                'statusCode': 500,
                'body': json.dumps(error_response)
            }
