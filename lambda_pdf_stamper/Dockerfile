FROM public.ecr.aws/lambda/python:3.11

# Instalar dependencias del sistema necesarias para PyMuPDF
RUN yum update -y && \
    yum install -y \
    gcc \
    gcc-c++ \
    make \
    && yum clean all

# Copiar requirements y instalar dependencias de Python
COPY requirements.txt /var/task
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código de la aplicación
COPY lambda_function.py pdf_stamper.py /var/task/

# Configurar el handler
CMD ["lambda_function.lambda_handler"] 