import json
import os
import boto3
from botocore.config import Config
from botocore.exceptions import ClientError
from enum import Enum
from typing import Dict, Any, Optional

class Operation(str, Enum):
    DOWNLOAD = "download"
    UPLOAD = "upload"
    DELETE = "delete"

class S3PresignedUrlGenerator:
    def __init__(self):
        # Configurar path-style addressing para object storage S3-compatible
        config = Config(
            s3={
                'addressing_style': 'path'
            }
        )
        
        # Configuración igual que el proyecto de referencia
        self.s3_client = boto3.client(
            's3',
            endpoint_url=os.getenv('S3_ENDPOINT_URL'),
            aws_access_key_id=os.getenv('S3_ACCESS_KEY'),
            aws_secret_access_key=os.getenv('S3_SECRET_KEY'),
            region_name=os.getenv('S3_REGION', 'us-east-1'),
            config=config
        )
    
    def generate_presigned_url(
        self,
        operation: str,
        bucket_name: str,
        object_key: str,
        expiration: int = 3600
    ) -> Optional[str]:
        """
        Genera URL prefirmada para operaciones S3
        
        Args:
            operation: Tipo de operación (download, upload, delete)
            bucket_name: Nombre del bucket S3
            object_key: Clave del objeto en S3
            expiration: Tiempo de expiración en segundos
            
        Returns:
            URL prefirmada o None si hay error
        """
        try:
            method_mapping = {
                Operation.DOWNLOAD: 'get_object',
                Operation.UPLOAD: 'put_object',
                Operation.DELETE: 'delete_object'
            }
            
            if operation not in method_mapping:
                raise ValueError(f"Operación no soportada: {operation}")
            
            method = method_mapping[operation]
            
            url = self.s3_client.generate_presigned_url(
                ClientMethod=method,
                Params={
                    'Bucket': bucket_name,
                    'Key': object_key
                },
                ExpiresIn=expiration
            )
            
            return url
            
        except ClientError as e:
            print(f"Error de cliente S3: {e}")
            return None
        except Exception as e:
            print(f"Error inesperado: {e}")
            return None

def authenticate_request(headers: Dict[str, str]) -> bool:
    """
    Valida el API key desde los headers
    
    Args:
        headers: Headers de la request
        
    Returns:
        True si la autenticación es exitosa
    """
    api_key = os.getenv('API_KEY', 'bPPYonPCqza4KXdKJVNS')  # Clave por defecto de 20 caracteres
    
    # Buscar el header x-api-key (case insensitive)
    request_api_key = None
    for key, value in headers.items():
        if key.lower() == 'x-api-key':
            request_api_key = value
            break
    
    if not request_api_key:
        return False
    
    return request_api_key == api_key

def parse_event(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parsea el evento para soportar tanto Lambda tradicional como Function URL
    Extrae bucket_name desde headers para mayor seguridad
    """
    # Detectar si es un evento de Function URL (HTTP request)
    if 'httpMethod' in event or 'requestContext' in event:
        # Es un evento de Function URL
        headers = event.get('headers', {})
        
        # Autenticar la request
        if not authenticate_request(headers):
            raise ValueError("API key inválido o faltante")
        
        # Extraer bucket_name desde headers
        bucket_name = None
        for key, value in headers.items():
            if key.lower() == 'x-bucket-name':
                bucket_name = value
                break
        
        if not bucket_name:
            raise ValueError("Header 'x-bucket-name' requerido")
        
        # Parsear body para otros parámetros
        parsed_data = {'bucket_name': bucket_name}
        
        try:
            if 'body' in event and event['body']:
                # El body puede venir como string, parsearlo
                if isinstance(event['body'], str):
                    body = json.loads(event['body'])
                else:
                    body = event['body']
                parsed_data.update(body)
            else:
                # Si no hay body, buscar en queryStringParameters
                query_params = event.get('queryStringParameters', {})
                if query_params:
                    parsed_data.update(query_params)
        except json.JSONDecodeError:
            raise ValueError("Body JSON inválido")
            
        return parsed_data
    else:
        # Es un evento de Lambda tradicional
        # Para backward compatibility, usar API key desde event si está presente
        if 'api_key' in event:
            api_key = os.getenv('API_KEY', 'bPPYonPCqza4KXdKJVNS')
            if event['api_key'] != api_key:
                raise ValueError("API key inválido")
        
        return event

def lambda_handler(event: Dict[str, Any], context) -> Dict[str, Any]:
    """
    Handler principal que soporta tanto eventos Lambda como Function URL
    
    Para Function URL (con seguridad):
    Headers requeridos:
    - x-api-key: Clave de autenticación
    - x-bucket-name: Nombre del bucket S3
    
    Body:
    {
        "operation": "download|upload|delete",
        "filepath": "ruta/del/archivo.ext",
        "expiration": 3600
    }
    
    Para Lambda tradicional:
    {
        "operation": "download|upload|delete",
        "bucket_name": "nombre-del-bucket",
        "filepath": "ruta/del/archivo.ext",
        "expiration": 3600,
        "api_key": "clave-opcional-para-validacion"
    }
    """
    try:
        # Parsear el evento (detecta automáticamente el formato y valida autenticación)
        parsed_event = parse_event(event)
        
        # Validar parámetros requeridos
        required_params = ['operation', 'bucket_name', 'filepath']
        missing_params = [param for param in required_params if param not in parsed_event]
        
        if missing_params:
            error_response = {
                'error': f'Parámetros faltantes: {", ".join(missing_params)}',
                'required_params': required_params,
                'security_note': 'Asegúrate de incluir headers: x-api-key, x-bucket-name'
            }
            
            # Formato de respuesta según el tipo de evento
            if 'httpMethod' in event or 'requestContext' in event:
                return {
                    'statusCode': 400,
                    'headers': {'Content-Type': 'application/json'},
                    'body': json.dumps(error_response)
                }
            else:
                return {
                    'statusCode': 400,
                    'body': json.dumps(error_response)
                }
        
        operation = parsed_event['operation'].lower()
        bucket_name = parsed_event['bucket_name']
        filepath = parsed_event['filepath']
        expiration = parsed_event.get('expiration', 3600)
        
        # Validar operación
        if operation not in [op.value for op in Operation]:
            error_response = {
                'error': f'Operación no válida: {operation}',
                'valid_operations': [op.value for op in Operation]
            }
            
            if 'httpMethod' in event or 'requestContext' in event:
                return {
                    'statusCode': 400,
                    'headers': {'Content-Type': 'application/json'},
                    'body': json.dumps(error_response)
                }
            else:
                return {
                    'statusCode': 400,
                    'body': json.dumps(error_response)
                }
        
        # Generar URL prefirmada
        generator = S3PresignedUrlGenerator()
        url = generator.generate_presigned_url(
            operation=operation,
            bucket_name=bucket_name,
            object_key=filepath,
            expiration=expiration
        )
        
        if url:
            success_response = {
                'url': url,
                'operation': operation,
                'bucket': bucket_name,
                'filepath': filepath,
                'expiration_seconds': expiration
            }
            
            # Formato de respuesta según el tipo de evento
            if 'httpMethod' in event or 'requestContext' in event:
                return {
                    'statusCode': 200,
                    'headers': {'Content-Type': 'application/json'},
                    'body': json.dumps(success_response)
                }
            else:
                return {
                    'statusCode': 200,
                    'body': json.dumps(success_response)
                }
        else:
            error_response = {'error': 'Error al generar la URL prefirmada'}
            
            if 'httpMethod' in event or 'requestContext' in event:
                return {
                    'statusCode': 500,
                    'headers': {'Content-Type': 'application/json'},
                    'body': json.dumps(error_response)
                }
            else:
                return {
                    'statusCode': 500,
                    'body': json.dumps(error_response)
                }
            
    except ValueError as e:
        # Errores de validación (autenticación, parámetros, etc.)
        error_response = {
            'error': str(e),
            'security_hint': 'Verifica headers: x-api-key, x-bucket-name'
        }
        
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': 401 if 'API key' in str(e) else 400,
                'headers': {'Content-Type': 'application/json'},
                'body': json.dumps(error_response)
            }
        else:
            return {
                'statusCode': 401 if 'API key' in str(e) else 400,
                'body': json.dumps(error_response)
            }
            
    except Exception as e:
        print(f"Error en lambda_handler: {e}")
        error_response = {'error': f'Error interno: {str(e)}'}
        
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': 500,
                'headers': {'Content-Type': 'application/json'},
                'body': json.dumps(error_response)
            }
        else:
            return {
                'statusCode': 500,
                'body': json.dumps(error_response)
            } 