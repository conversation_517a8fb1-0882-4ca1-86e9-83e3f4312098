#!/bin/bash

# Variables de configuración
AWS_REGION=${AWS_REGION:-"us-east-1"}
ECR_REPO_NAME="gedsys/gedsystool-stamper"
LAMBDA_FUNCTION_NAME=${LAMBDA_FUNCTION_NAME:-"s3-presigned-url-generator"}
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

echo "🚀 Iniciando despliegue de Lambda S3 Presigned URL..."
echo "Región: $AWS_REGION"
echo "Repositorio ECR: $ECR_REPO_NAME"
echo "Función Lambda: $LAMBDA_FUNCTION_NAME"
echo "Account ID: $AWS_ACCOUNT_ID"


# 2. Obtener login para ECR
echo "🔐 Obteniendo login para ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# 3. Build de la imagen Docker
echo "🏗️ Construyendo imagen Docker..."
docker build --platform linux/arm64 -t s3-lambda-temp .

# 4. Tag de la imagen
ECR_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPO_NAME:latest"
echo "🏷️ Taggeando imagen: $ECR_URI"
docker tag s3-lambda-temp:latest $ECR_URI

# 5. Push a ECR
echo "⬆️ Subiendo imagen a ECR..."
docker push $ECR_URI

# 6. Crear o actualizar función Lambda
echo "⚡ Desplegando función Lambda..."

# Verificar si la función existe
aws lambda get-function --function-name $LAMBDA_FUNCTION_NAME --region $AWS_REGION 2>/dev/null && {
    echo "Actualizando función Lambda existente..."
    aws lambda update-function-code \
        --function-name $LAMBDA_FUNCTION_NAME \
        --image-uri $ECR_URI \
        --region $AWS_REGION
} || {
    echo "Creando nueva función Lambda..."
    
    # Crear rol IAM si no existe
    ROLE_NAME="lambda-s3-presigned-url-role"
    aws iam get-role --role-name $ROLE_NAME 2>/dev/null || {
        echo "Creando rol IAM para Lambda..."
        aws iam create-role \
            --role-name $ROLE_NAME \
            --assume-role-policy-document '{
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Principal": {
                            "Service": "lambda.amazonaws.com"
                        },
                        "Action": "sts:AssumeRole"
                    }
                ]
            }'
        
        # Adjuntar políticas necesarias
        aws iam attach-role-policy \
            --role-name $ROLE_NAME \
            --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        
        aws iam attach-role-policy \
            --role-name $ROLE_NAME \
            --policy-arn arn:aws:iam::aws:policy/AmazonS3FullAccess
        
        # Esperar a que el rol esté disponible
        echo "Esperando a que el rol esté disponible..."
        sleep 10
    }
    
    ROLE_ARN="arn:aws:iam::$AWS_ACCOUNT_ID:role/$ROLE_NAME"
    
    aws lambda create-function \
        --function-name $LAMBDA_FUNCTION_NAME \
        --package-type Image \
        --code ImageUri=$ECR_URI \
        --role $ROLE_ARN \
        --timeout 30 \
        --memory-size 256 --architectures arm64 \
        --region $AWS_REGION
}

echo "✅ Despliegue completado!"
echo "Función Lambda: $LAMBDA_FUNCTION_NAME"
echo "URI de imagen ECR: $ECR_URI"
echo ""
echo "Para probar la función, ejecuta:"
echo "aws lambda invoke --function-name $LAMBDA_FUNCTION_NAME --payload '{\"operation\":\"download\",\"bucket_name\":\"tu-bucket\",\"filepath\":\"test.txt\"}' response.json && cat response.json" 