# Configuración S3 para Lambda

La lambda funciona exactamente igual que el **proyecto de referencia**, usando **solo variables de entorno** para la configuración del object storage S3-compatible.

## 🔧 Configuración mediante Variables de Entorno

**ÚNICA FORMA DE CONFIGURACIÓN** - siguiendo el patrón del proyecto de referencia:

```bash
# Configurar variables de entorno en la Lambda
aws lambda update-function-configuration \
    --function-name s3-presigned-url-generator \
    --environment Variables='{
        "S3_ENDPOINT_URL":"https://tu-endpoint-s3.com",
        "S3_ACCESS_KEY":"tu-access-key",
        "S3_SECRET_KEY":"tu-secret-key",
        "S3_REGION":"us-east-1"
    }'
```

## 📋 Variables de Entorno

| Variable | Descripción | Requerido | Ejemplo |
|----------|-------------|-----------|---------|
| `S3_ENDPOINT_URL` | URL del endpoint S3 | ✅ | `https://tu-endpoint-s3.com` |
| `S3_ACCESS_KEY` | Access Key | ✅ | `AKIAIOSFODNN7EXAMPLE` |
| `S3_SECRET_KEY` | Secret Key | ✅ | `wJalrXUtnFEMI/K7MDENG...` |
| `S3_REGION` | Región | ❌ | `us-east-1` (default) |

## 📝 Formato del Evento

**Evento simplificado** (igual que el proyecto de referencia):

```json
{
    "operation": "download|upload|delete",
    "bucket_name": "nombre-del-bucket",
    "filepath": "ruta/del/archivo.ext",
    "expiration": 3600
}
```

## 🎯 Casos de Uso

### **MinIO**
```bash
S3_ENDPOINT_URL=https://minio.example.com:9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin
S3_REGION=us-east-1
```

### **DigitalOcean Spaces**
```bash
S3_ENDPOINT_URL=https://nyc3.digitaloceanspaces.com
S3_ACCESS_KEY=tu-do-access-key
S3_SECRET_KEY=tu-do-secret-key
S3_REGION=nyc3
```

### **Wasabi**
```bash
S3_ENDPOINT_URL=https://s3.wasabisys.com
S3_ACCESS_KEY=tu-wasabi-access-key
S3_SECRET_KEY=tu-wasabi-secret-key
S3_REGION=us-east-1
```

## 🧪 Pruebas

### **Probar con AWS CLI**

```bash
# Ejemplo de invocación
aws lambda invoke \
    --function-name s3-presigned-url-generator \
    --payload '{
        "operation":"download",
        "bucket_name":"mi-bucket",
        "filepath":"documents/archivo.pdf",
        "expiration":3600
    }' \
    response.json

cat response.json
```

## 🚀 Redespliegue

Después de actualizar el código:

```bash
./deploy.sh
```

## 📊 Comparación con Proyecto de Referencia

| Aspecto | Proyecto FastAPI | Lambda Container |
|---------|------------------|------------------|
| **Configuración** | Variables entorno (.env) | Variables entorno (Lambda) |
| **Path-style** | ✅ Config automático | ✅ Config automático |
| **Endpoint personalizado** | ✅ S3_ENDPOINT_URL | ✅ S3_ENDPOINT_URL |
| **Credenciales** | ✅ S3_ACCESS_KEY/SECRET | ✅ S3_ACCESS_KEY/SECRET |
| **Formato URLs** | Idéntico | Idéntico |
| **Configuración** | **Una sola forma** | **Una sola forma** | 