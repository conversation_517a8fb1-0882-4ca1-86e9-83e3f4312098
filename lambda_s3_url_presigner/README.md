# Lambda S3 Presigned URL Generator

Lambda de AWS containerizada que genera URLs prefirmadas para operaciones en S3 (download, upload, delete) usando boto3. **Funciona exactamente igual que el proyecto de referencia con object storage S3-compatible y path-style addressing.** 

🔐 **Ahora incluye autenticación por API Key y headers de seguridad.**

## 📋 Características

- ✅ **Generación de URLs prefirmadas** para operaciones S3
- ✅ **Object Storage S3-compatible** (MinIO, DigitalOcean Spaces, Wasabi, etc.)
- ✅ **Path-style addressing** automático para compatibilidad total
- ✅ **Configuración mediante variables de entorno** (igual que proyecto de referencia)
- ✅ **Soporte para múltiples operaciones**: download, upload, delete
- ✅ **Configuración de expiración** personalizable
- ✅ **Despliegue containerizado** en AWS ECR
- ✅ **Validación robusta** de parámetros de entrada
- ✅ **Manejo de errores** comprehensivo
- 🔐 **Autenticación por API Key** para mayor seguridad
- 🔐 **Headers de seguridad** para proteger información sensible

## 🏗️ Arquitectura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────┐
│   API Gateway   │────│  Lambda (ECR)   │────│ Object Storage      │
│   o invocador   │    │   Container     │    │ S3-Compatible       │
│  (con API Key)  │    │ (Autenticado)   │    │                     │
└─────────────────┘    └─────────────────┘    └─────────────────────┘
```

## 🚀 Despliegue Rápido

### Prerrequisitos

- AWS CLI configurado con credenciales apropiadas
- Docker instalado
- Permisos IAM para:
  - ECR (crear repositorio, push/pull imágenes)
  - Lambda (crear/actualizar funciones)
  - IAM (crear roles y políticas)
  - S3 (para generar URLs prefirmadas)

### Despliegue Automatizado

```bash
# Usar configuración por defecto
./deploy.sh

# O configurar variables de entorno
export AWS_REGION="us-west-2"
export ECR_REPO_NAME="mi-lambda-s3"
export LAMBDA_FUNCTION_NAME="mi-generador-urls"
./deploy.sh
```

## 🔐 Configuración de Seguridad

### API Key

La lambda incluye autenticación por API Key. **Por defecto se genera una clave segura de 20 caracteres**, pero se recomienda configurar una personalizada:

```bash
# Configurar API Key personalizada
aws lambda update-function-configuration \
    --function-name s3-presigned-url-generator \
    --environment Variables='{
        "API_KEY":"tu-clave-segura-personalizada",
        "S3_ENDPOINT_URL":"https://tu-endpoint-s3.com",
        "S3_ACCESS_KEY":"tu-access-key", 
        "S3_SECRET_KEY":"tu-secret-key",
        "S3_REGION":"us-east-1"
    }'
```

### Clave por Defecto

Si no configuras `API_KEY`, se usa la clave por defecto: `bPPYonPCqza4KXdKJVNS`

⚠️ **Recomendación**: Cambia esta clave en producción.

## 📝 Uso de la Lambda

### Headers Requeridos

| Header | Descripción | Ejemplo |
|--------|-------------|---------|
| `x-api-key` | Clave de autenticación | `bPPYonPCqza4KXdKJVNS` |
| `x-bucket-name` | Nombre del bucket S3 | `mi-bucket-produccion` |

### Formato del Evento de Entrada

#### Para Function URL (HTTP):
```bash
curl -X POST https://tu-function-url.lambda-url.region.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: bPPYonPCqza4KXdKJVNS" \
  -H "x-bucket-name: mi-bucket" \
  -d '{
    "operation": "download",
    "filepath": "documents/archivo.pdf",
    "expiration": 3600
  }'
```

#### Para invocación directa de Lambda:
```json
{
  "operation": "download|upload|delete",
  "bucket_name": "nombre-del-bucket",
  "filepath": "ruta/del/archivo.ext",
  "expiration": 3600,
  "api_key": "bPPYonPCqza4KXdKJVNS"
}
```

### Parámetros

| Parámetro | Ubicación | Tipo | Requerido | Descripción |
|-----------|-----------|------|-----------|-------------|
| `x-api-key` | Header | string | ✅ | Clave de autenticación |
| `x-bucket-name` | Header | string | ✅ | Nombre del bucket S3 |
| `operation` | Body | string | ✅ | Tipo de operación: "download", "upload" o "delete" |
| `filepath` | Body | string | ✅ | Ruta del archivo en el bucket |
| `expiration` | Body | integer | ❌ | Tiempo de expiración en segundos (default: 3600) |

### Ejemplos de Uso

#### Function URL - Generar URL para Descarga
```bash
curl -X POST https://x6xuqv3rzcfmuo66g3amerm7ly0gzibd.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: bPPYonPCqza4KXdKJVNS" \
  -H "x-bucket-name: gedsys-development" \
  -d '{
    "operation": "download",
    "filepath": "avatar/jmarin.png",
    "expiration": 3600
  }'
```

#### Function URL - Generar URL para Subida
```bash
curl -X POST https://x6xuqv3rzcfmuo66g3amerm7ly0gzibd.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: bPPYonPCqza4KXdKJVNS" \
  -H "x-bucket-name: mi-bucket" \
  -d '{
    "operation": "upload",
    "filepath": "images/nueva-imagen.jpg",
    "expiration": 1800
  }'
```

#### Invocación Directa - Generar URL para Descarga
```bash
aws lambda invoke \
  --function-name s3-presigned-url-generator \
  --payload '{
    "operation": "download",
    "bucket_name": "mi-bucket",
    "filepath": "documents/archivo.pdf",
    "expiration": 3600,
    "api_key": "bPPYonPCqza4KXdKJVNS"
  }' \
  response.json
```

### Respuesta Exitosa (200)

```json
{
  "statusCode": 200,
  "body": "{
    \"url\": \"https://mi-bucket.s3.amazonaws.com/archivo.pdf?AWSAccessKeyId=...\",
    \"operation\": \"download\",
    \"bucket\": \"mi-bucket\",
    \"filepath\": \"documents/archivo.pdf\",
    \"expiration_seconds\": 3600,
    \"addressing_style\": \"path\",
    \"security\": \"authenticated\"
  }"
}
```

### Respuestas de Error

#### Error de Autenticación (401)
```json
{
  "statusCode": 401,
  "body": "{
    \"error\": \"API key inválido o faltante\",
    \"security_hint\": \"Verifica headers: x-api-key, x-bucket-name\"
  }"
}
```

#### Error de Parámetros (400)
```json
{
  "statusCode": 400,
  "body": "{
    \"error\": \"Header 'x-bucket-name' requerido\",
    \"security_hint\": \"Verifica headers: x-api-key, x-bucket-name\"
  }"
}
```

## 🔧 Desarrollo Local

### Probar Lambda Localmente

```bash
# Instalar dependencias
pip install -r requirements.txt

# Ejecutar pruebas con autenticación
python -c "
import json
from lambda_function import lambda_handler

# Simular evento de Function URL
event = {
    'httpMethod': 'POST',
    'headers': {
        'x-api-key': 'bPPYonPCqza4KXdKJVNS',
        'x-bucket-name': 'test-bucket'
    },
    'body': json.dumps({
        'operation': 'download',
        'filepath': 'test.txt'
    })
}

result = lambda_handler(event, None)
print(json.dumps(result, indent=2))
"
```

### Build Local del Container

```bash
# Build de la imagen
docker build -t s3-presigned-url-lambda .

# Probar localmente (requiere AWS credentials y API_KEY)
docker run --rm \
  -e AWS_ACCESS_KEY_ID \
  -e AWS_SECRET_ACCESS_KEY \
  -e AWS_SESSION_TOKEN \
  -e AWS_DEFAULT_REGION \
  -e API_KEY="tu-clave-personalizada" \
  s3-presigned-url-lambda
```

## 🔐 Permisos IAM

La lambda requiere los siguientes permisos mínimos:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject", 
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::tu-bucket/*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*"
    }
  ]
}
```

## 📁 Estructura del Proyecto

```
lambda_s3/
├── lambda_function.py      # Función principal de la lambda
├── Dockerfile             # Configuración del container
├── requirements.txt       # Dependencias Python
├── deploy.sh             # Script de despliegue automatizado
└── README.md            # Esta documentación
```

## 🚨 Consideraciones de Seguridad

1. **Autenticación por API Key**: Todas las requests requieren header `x-api-key` válido
2. **Headers seguros**: El `bucket_name` se pasa por headers, no en el body
3. **Validación de Entrada**: La lambda valida todos los parámetros de entrada
4. **Manejo de Errores**: No expone información sensible en mensajes de error
5. **Permisos Mínimos**: Use el principio de menor privilegio para permisos IAM
6. **Expiración de URLs**: Configure tiempos de expiración apropiados según su caso de uso
7. **Rotación de Claves**: Cambie el API Key regularmente en producción

### Mejores Prácticas de Seguridad

```bash
# 1. Configurar API Key único por ambiente
export API_KEY_PROD="clave-super-segura-produccion-32chars"
export API_KEY_DEV="clave-desarrollo-testing-24chars"

# 2. Rotar claves periódicamente
aws lambda update-function-configuration \
    --function-name s3-presigned-url-generator \
    --environment Variables='{"API_KEY":"nueva-clave-rotada"}'

# 3. Monitorear intentos de acceso no autorizados
aws logs filter-log-events \
    --log-group-name /aws/lambda/s3-presigned-url-generator \
    --filter-pattern "API key inválido"
```

## 🔄 Actualización

Para actualizar la lambda con cambios en el código:

```bash
# El script detecta automáticamente si la función existe y la actualiza
./deploy.sh
```

## 📊 Monitoreo

La lambda genera logs en CloudWatch. Para monitorear:

```bash
# Ver logs recientes
aws logs tail /aws/lambda/s3-presigned-url-generator --follow

# Filtrar intentos de acceso no autorizados
aws logs filter-log-events \
    --log-group-name /aws/lambda/s3-presigned-url-generator \
    --filter-pattern "401"

# Métricas de la función
aws lambda get-function --function-name s3-presigned-url-generator
```

## 🤝 Comparación con la API Original

Esta lambda mantiene la **misma funcionalidad** que la API FastAPI de referencia, pero con **mejoras de seguridad** y adaptada para:

- ✅ **Ejecución serverless** en AWS Lambda
- ✅ **Despliegue containerizado** en ECR
- ✅ **Escalado automático** y sin gestión de infraestructura
- ✅ **Integración nativa** con el ecosistema AWS
- 🔐 **Autenticación por API Key** (mejora de seguridad)
- 🔐 **Headers seguros** para información sensible

| Característica | API FastAPI | Lambda Container |
|----------------|-------------|------------------|
| Operaciones S3 | ✅ | ✅ |
| URLs Prefirmadas | ✅ | ✅ |
| Validación | ✅ | ✅ |
| Manejo Errores | ✅ | ✅ |
| Autenticación | ❌ | ✅ API Key |
| Headers Seguros | ❌ | ✅ x-bucket-name |
| Escalabilidad | Manual | Automática |
| Costo | Servidor continuo | Pay-per-use |
| Despliegue | Servidor/Container | ECR + Lambda | 

## 🔧 Configuración Completa

**Configurar todas las variables de entorno** (S3 + seguridad):

```bash
aws lambda update-function-configuration \
    --function-name s3-presigned-url-generator \
    --environment Variables='{
        "API_KEY":"tu-clave-segura-personalizada-20chars",
        "S3_ENDPOINT_URL":"https://tu-endpoint-s3.com",
        "S3_ACCESS_KEY":"tu-access-key",
        "S3_SECRET_KEY":"tu-secret-key",
        "S3_REGION":"us-east-1"
    }'
```

## 🔗 Function URL Activa

Tu lambda ya está desplegada y disponible en:
```
https://x6xuqv3rzcfmuo66g3amerm7ly0gzibd.lambda-url.us-east-1.on.aws/
```

**Ejemplo de uso inmediato:**
```bash
curl -X POST https://x6xuqv3rzcfmuo66g3amerm7ly0gzibd.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: bPPYonPCqza4KXdKJVNS" \
  -H "x-bucket-name: gedsys-development" \
  -d '{"operation":"download","filepath":"avatar/jmarin.png"}'
``` 