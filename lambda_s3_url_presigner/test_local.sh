#!/bin/bash

# Script de prueba para la lambda S3 Presigned URL Generator
# La lambda está corriendo en Docker en el puerto 9000

echo "🧪 Probando lambda S3 Presigned URL Generator (local)"
echo "================================================================"

# URL base de la lambda local
LAMBDA_URL="http://localhost:9000/2015-03-31/functions/function/invocations"

echo ""
echo "1. Probando operación DOWNLOAD:"
echo "------------------------------"
curl -s -X POST $LAMBDA_URL \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "download",
    "bucket_name": "test-bucket",
    "filepath": "test-file.txt",
    "expiration": 3600,
    "api_key": "bPPYonPCqza4KXdKJVNS"
  }' | jq

echo ""
echo "2. Probando operación UPLOAD:"
echo "-----------------------------"
curl -s -X POST $LAMBDA_URL \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "upload",
    "bucket_name": "test-bucket",
    "filepath": "upload-file.pdf",
    "expiration": 7200,
    "api_key": "bPPYonPCqza4KXdKJVNS"
  }' | jq

echo ""
echo "3. Probando operación DELETE:"
echo "-----------------------------"
curl -s -X POST $LAMBDA_URL \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "delete",
    "bucket_name": "test-bucket",
    "filepath": "delete-file.txt",
    "api_key": "bPPYonPCqza4KXdKJVNS"
  }' | jq

echo ""
echo "4. Probando con API key inválido (debería fallar):"
echo "---------------------------------------------------"
curl -s -X POST $LAMBDA_URL \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "download",
    "bucket_name": "test-bucket",
    "filepath": "test-file.txt",
    "api_key": "wrong-key"
  }' | jq

echo ""
echo "================================================================"
echo "✅ Pruebas completadas!"
echo ""
echo "Para parar el contenedor: docker stop s3-presigner-container"
echo "Para reiniciar el contenedor: docker start s3-presigner-container"
echo "Para ver logs: docker logs s3-presigner-container" 