FROM public.ecr.aws/lambda/python:3.11

# Instalar fuentes necesarias
RUN yum update -y && \
    yum install -y \
    fontconfig \
    dejavu-sans-fonts \
    liberation-sans-fonts \
    && yum clean all

# Crear enlaces simbólicos para compatibilidad con Arial
RUN mkdir -p /usr/share/fonts/truetype/liberation && \
    ln -sf /usr/share/fonts/liberation /usr/share/fonts/truetype/liberation && \
    ln -sf /usr/share/fonts/truetype/liberation /usr/share/fonts/truetype/arial && \
    fc-cache -f

# Copiar requirements y instalar dependencias
COPY requirements.txt /var/task
RUN pip install -r requirements.txt

# Copiar el código de la función y archivos necesarios
COPY lambda_function.py /var/task
COPY stamp_generator.py /var/task
COPY bw-icon.png /var/task

# Configurar el handler
CMD ["lambda_function.lambda_handler"] 