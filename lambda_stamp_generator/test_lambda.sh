#!/bin/bash

# Script de prueba para Lambda Stamp Generator con S3

FUNCTION_URL="https://ks46n7nohzgvtgmfdi3ntxkvom0qaupq.lambda-url.us-east-1.on.aws/"

echo "🧪 Probando Lambda Stamp Generator..."
echo "=================================="
echo ""

# Prueba 1: Sin logo (no requiere S3 configurado)
echo "📝 Prueba 1: Estampilla básica sin logo"
echo "Esta prueba debería fallar con las credenciales S3 dummy pero mostrará que la Lambda está funcionando"
echo ""

curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-api-key: StampGenerator2024Key" \
  -H "x-bucket-name: mi-bucket-prueba" \
  -d '{
    "title": "Estampilla de Prueba",
    "properties": [
        {"label": "Documento", "value": "DOC-TEST-001"},
        {"label": "Fecha", "value": "'$(date +%Y-%m-%d)'"},
        {"label": "Estado", "value": "PRUEBA"},
        {"label": "Hora", "value": "'$(date +%H:%M:%S)'"}
    ],
    "qr_content": "https://ejemplo.com/verificar/test001",
    "output_filename": "test-stamp-'$(date +%s)'.png",
    "expiration_seconds": 3600,
    "transparent_background": false
}' | python3 -m json.tool

echo ""
echo ""
echo "=================================="
echo "📝 Prueba 2: Con logo (requiere S3 configurado)"
echo "Esta prueba fallará porque requiere credenciales S3 reales"
echo ""

curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-api-key: StampGenerator2024Key" \
  -H "x-bucket-name: mi-bucket-real" \
  -d '{
    "title": "Certificado Digital",
    "properties": [
        {"label": "ID", "value": "CERT-2024-001"},
        {"label": "Emisor", "value": "Sistema GEDSYS"},
        {"label": "Válido hasta", "value": "2025-01-15"}
    ],
    "qr_content": "https://gedsys.com/certificados/2024/001",
    "logo_filename": "logo-empresa.png",
    "output_filename": "certificado-'$(date +%s)'.png",
    "expiration_seconds": 7200,
    "transparent_background": true
}' | python3 -m json.tool

echo ""
echo ""
echo "=================================="
echo "✅ Pruebas completadas"
echo ""
echo "⚠️  NOTA: Los errores relacionados con S3 son esperados porque las credenciales son DUMMY"
echo "Para usar S3 real, actualiza las variables de entorno con:"
echo ""
echo "aws lambda update-function-configuration \\"
echo "  --function-name stamp-generator \\"
echo "  --environment \"Variables={...credenciales-reales...}\" \\"
echo "  --region us-east-1" 