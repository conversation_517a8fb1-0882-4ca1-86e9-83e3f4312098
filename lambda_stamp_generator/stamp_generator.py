import io
import uuid
import qrcode
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime
import os
import logging

# Configurar logging solo para errores
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("stamp-generator")

class StampGenerator:
    # Constantes para dimensiones 
    FIXED_WIDTH = 600
    FIXED_HEIGHT = 350
    QR_MARGIN = 15
    QR_SIZE = 150  # Aumentado de 120px a 150px
    LOGO_SIZE = 130  # Tamaño del logo más grande
    MARGIN_X = 15
    MARGIN_Y = 25
    MAX_PROPERTIES = 5
    QR_LOGO_PATH = "bw-icon.png"  # Ruta al icono predeterminado para el centro del QR
    
    # Colores
    COLOR_BORDER = (0, 48, 135)
    COLOR_TITLE = (0, 48, 135)
    COLOR_TEXT = (33, 33, 33)
    COLOR_LABEL = (100, 100, 100)
    BACKGROUND_COLOR = (255, 255, 255, 255)
    TRANSPARENT_COLOR = (0, 0, 0, 0)

    def __init__(self):
        try:
            # Buscar fuentes Liberation o Arial en rutas específicas
            # Actualizado para las rutas correctas en Lambda
            font_dirs = [
                "/usr/share/fonts/liberation",  # Ruta real en Lambda
                "/usr/share/fonts/dejavu",      # Ruta alternativa
                "/usr/share/fonts/truetype/liberation",
                "/usr/share/fonts/truetype/arial",
                "/usr/share/fonts/truetype"
            ]
            
            # Buscar la fuente en cada directorio
            found_bold = False
            found_regular = False
            
            for font_dir in font_dirs:
                try:
                    # Intentar con Liberation Sans (alternativa a Arial en Linux)
                    if not found_bold:
                        liberation_bold = os.path.join(font_dir, "LiberationSans-Bold.ttf")
                        if os.path.exists(liberation_bold):
                            self.title_font = lambda size: ImageFont.truetype(liberation_bold, size)
                            self.bold_font = lambda size: ImageFont.truetype(liberation_bold, size)
                            found_bold = True
                    
                    if not found_regular:
                        liberation_regular = os.path.join(font_dir, "LiberationSans-Regular.ttf")
                        if os.path.exists(liberation_regular):
                            self.normal_font = lambda size: ImageFont.truetype(liberation_regular, size)
                            found_regular = True
                    
                    if found_bold and found_regular:
                        break
                except OSError as e:
                    logger.warning(f"Error al cargar fuentes desde {font_dir}: {str(e)}")
            
            # Si no se encontraron todas las fuentes, intentar con fuentes del sistema
            if not (found_bold and found_regular):
                logger.warning("No se pudieron cargar todas las fuentes Liberation, intentando con fuentes del sistema")
                try:
                    # Intentar con Arial (macOS/Windows) o fuentes similares
                    self.title_font = lambda size: ImageFont.truetype("Arial Bold", size)
                    self.bold_font = lambda size: ImageFont.truetype("Arial Bold", size) 
                    self.normal_font = lambda size: ImageFont.truetype("Arial", size)
                except OSError:
                    try:
                        # Fallback a Helvetica en macOS
                        self.title_font = lambda size: ImageFont.truetype("Helvetica-Bold", size)
                        self.bold_font = lambda size: ImageFont.truetype("Helvetica-Bold", size)
                        self.normal_font = lambda size: ImageFont.truetype("Helvetica", size)
                    except OSError:
                        # Último fallback - usar DejaVu que suele estar disponible en Linux
                        try:
                            self.title_font = lambda size: ImageFont.truetype("/usr/share/fonts/dejavu/DejaVuSans-Bold.ttf", size)
                            self.bold_font = lambda size: ImageFont.truetype("/usr/share/fonts/dejavu/DejaVuSans-Bold.ttf", size)
                            self.normal_font = lambda size: ImageFont.truetype("/usr/share/fonts/dejavu/DejaVuSans.ttf", size)
                        except OSError:
                            # Si nada funciona, crear fuentes escaladas desde la fuente por defecto
                            logger.warning("No se pudieron cargar fuentes TrueType, usando fuente por defecto escalada")
                            # Crear fuentes con tamaños específicos para cada uso
                            self.title_font = lambda size: self._get_scaled_default_font(size)
                            self.bold_font = lambda size: self._get_scaled_default_font(size)
                            self.normal_font = lambda size: self._get_scaled_default_font(size)
                
        except Exception as e:
            logger.error(f"Error al configurar fuentes: {str(e)}")
            # Usar fuente por defecto escalada
            self.title_font = lambda size: self._get_scaled_default_font(size)
            self.bold_font = lambda size: self._get_scaled_default_font(size)  
            self.normal_font = lambda size: self._get_scaled_default_font(size)

    def _get_scaled_default_font(self, size):
        """
        Retorna una fuente por defecto que mantiene la proporcionalidad del tamaño solicitado.
        Cuando no hay fuentes TrueType disponibles, simula el tamaño escalando la fuente por defecto.
        """
        try:
            # Intentar usar una fuente del sistema que sea básica y esté disponible
            return ImageFont.truetype("arial.ttf", size)
        except OSError:
            try:
                return ImageFont.truetype("DejaVuSans.ttf", size)
            except OSError:
                # Como último recurso, usar la fuente por defecto
                # aunque no respete el tamaño, al menos será consistente
                return ImageFont.load_default()

    def generate_stamp(self, title, properties, qr_content, logo_image=None, transparent_background=False):
        """
        Genera una estampilla como BytesIO usando un mapa de propiedades y un logo opcional
        
        Args:
            title (str): Título de la estampilla
            properties (dict): Diccionario de propiedades (clave-valor) a mostrar
            qr_content (str): Contenido a codificar en el QR
            logo_image (str, optional): Ruta a la imagen del logo. Por defecto None.
            transparent_background (bool, optional): Si es True, el fondo será transparente. Por defecto False.
        """
        # Limitar el número de propiedades a MAX_PROPERTIES
        if len(properties) > self.MAX_PROPERTIES:
            logger.warning(f"El número de propiedades excede el límite de {self.MAX_PROPERTIES}. Se mostrarán solo las primeras {self.MAX_PROPERTIES}.")
            properties = dict(list(properties.items())[:self.MAX_PROPERTIES])
        
        # Verificar logo_image si se proporciona
        if logo_image and not os.path.exists(logo_image):
            logger.error(f"El archivo de logo no existe: {logo_image}")

        # Tamaños de texto para cálculos preliminares
        title_font_size = 28
        normal_font_size = 18
        label_font_size = 16

        # Crear una imagen temporal para medir el texto
        temp_img = Image.new('RGBA', (1, 1), (0, 0, 0, 0))
        temp_draw = ImageDraw.Draw(temp_img)

        # Calcular el ancho necesario para el texto
        text_width = self._calculate_text_width(temp_draw, title, properties, title_font_size, normal_font_size, label_font_size)
        
        # Usar el tamaño fijo para el QR
        qr_size = self.QR_SIZE
        
        # Calcular espacio para el logo si existe
        if logo_image:
            logo_size = self.LOGO_SIZE
        else:
            logo_size = 0
            
        # Calcular el ancho total necesario
        if logo_image:
            total_width = logo_size + text_width + qr_size + (4 * self.MARGIN_X)
        else:
            total_width = text_width + qr_size + (3 * self.MARGIN_X)

        # Asegurar un mínimo de ancho y asegurarse de que todas las dimensiones sean enteros
        width = int(max(total_width, self.FIXED_WIDTH))
        height = int(self.FIXED_HEIGHT)

        # Crear imagen con tamaño calculado y fondo según el parámetro
        background_color = self.TRANSPARENT_COLOR if transparent_background else self.BACKGROUND_COLOR
        image = Image.new('RGBA', (width, height), background_color)
        draw = ImageDraw.Draw(image)

        # Dibujar borde con esquinas redondeadas
        self._draw_rounded_rectangle(draw, [2, 2, width-3, height-3], 10, self.COLOR_BORDER, 2)

        # Posicionar QR en la esquina derecha centrado verticalmente
        qr_x = width - qr_size - self.QR_MARGIN
        qr_y = (height - qr_size) // 2

        # Generar y dibujar código QR (siempre con el logo predeterminado)
        qr_image = self._generate_qr_code(qr_content, qr_size)
        image.paste(qr_image, (qr_x, qr_y))

        # Posición inicial del texto (considerando el logo si existe)
        text_start_x = self.MARGIN_X
        
        # Procesar y dibujar logo si existe
        if logo_image:
            try:
                original_logo = Image.open(logo_image)
                
                if original_logo:
                    logo_y = (height - logo_size) // 2  # Centrar verticalmente
                    logo_x = self.MARGIN_X

                    resized_logo = self._resize_image(original_logo, logo_size, logo_size)
                    image.paste(resized_logo, (logo_x, logo_y), resized_logo if resized_logo.mode == 'RGBA' else None)
                    text_start_x = logo_x + logo_size + self.MARGIN_X
            except Exception as e:
                logger.error(f"Error al procesar el logo: {str(e)}", exc_info=True)

        # Calcular espacios disponibles
        available_height = height - (2 * self.MARGIN_Y)
        available_width = qr_x - text_start_x - self.MARGIN_X

        # Posición inicial del texto
        text_x = text_start_x
        text_y = self.MARGIN_Y

        # Calcular espacio para cada sección
        if title:
            title_height = title_font_size + 15
        else:
            title_height = 0

        # Espacio disponible para propiedades después del título
        content_height = available_height - title_height

        # Espacio por propiedad (cada propiedad tiene etiqueta + valor)
        property_height = content_height / len(properties)
        
        # Dibujar título
        if title:
            title_font = self.title_font(title_font_size)
            draw.text((text_x, text_y), title, fill=self.COLOR_TITLE, font=title_font)
            text_y += title_font_size + 15

        # Procesar propiedades
        for i, (key, value) in enumerate(properties.items()):
            # Espacio para la propiedad actual
            property_y_start = text_y
            
            # Dibujar etiqueta
            draw.text((text_x, text_y), key, fill=self.COLOR_LABEL, font=self.normal_font(label_font_size))
            text_y += label_font_size + 2
            
            # Valor puede ser multilínea - primero calculamos cuántas líneas
            lines = self._wrap_text(value, available_width, draw, self.bold_font(normal_font_size))
            
            # Dibujar primera línea
            draw.text((text_x, text_y), lines[0], fill=self.COLOR_TEXT, font=self.bold_font(normal_font_size))
            
            # Dibujar líneas adicionales si hay
            for line in lines[1:]:
                text_y += normal_font_size
                draw.text((text_x, text_y), line, fill=self.COLOR_TEXT, font=self.bold_font(normal_font_size))
            
            # Avanzar a la siguiente propiedad, pero asegurándose de tener espacio suficiente
            if i < len(properties) - 1:
                # Si no es la última propiedad, calcular el espacio disponible para la siguiente
                next_property_y = property_y_start + property_height
                
                # Asegurarse de que haya al menos un espacio mínimo después del texto actual
                min_next_y = text_y + normal_font_size + 10
                
                # Usar el valor mayor para evitar solapamientos
                text_y = max(next_property_y, min_next_y)
            else:
                # Es la última propiedad (fecha), añadir espacio extra
                text_y += normal_font_size + 5

        # Convertir a BytesIO
        output = io.BytesIO()
        image.save(output, format='PNG')
        output.seek(0)
        return output

    def _calculate_text_width(self, draw, title, properties, title_size, normal_size, label_size):
        """Calcula el ancho necesario para los textos sin márgenes adicionales"""
        max_width = 0
        
        # Medir título
        if title:
            title_width = draw.textlength(title, font=self.title_font(title_size))
            max_width = max(max_width, title_width)
        
        # Medir todas las propiedades
        for key, value in properties.items():
            # Medir etiqueta
            key_width = draw.textlength(key, font=self.normal_font(label_size))
            
            # Medir valor
            value_width = draw.textlength(value, font=self.bold_font(normal_size))
            
            property_width = max(key_width, value_width)
            max_width = max(max_width, property_width)
        
        # Agregar un pequeño margen para evitar que el texto quede muy justo
        return int(max_width + (2 * self.MARGIN_X))

    def _draw_rounded_rectangle(self, draw, coords, radius, color, width=1):
        """Dibuja un rectángulo con esquinas redondeadas"""
        x1, y1, x2, y2 = coords
        
        # Dibujar líneas principales
        draw.line([(x1 + radius, y1), (x2 - radius, y1)], fill=color, width=width)  # Top
        draw.line([(x1 + radius, y2), (x2 - radius, y2)], fill=color, width=width)  # Bottom
        draw.line([(x1, y1 + radius), (x1, y2 - radius)], fill=color, width=width)  # Left
        draw.line([(x2, y1 + radius), (x2, y2 - radius)], fill=color, width=width)  # Right
        
        # Dibujar esquinas redondeadas
        draw.arc([x1, y1, x1 + radius * 2, y1 + radius * 2], 180, 270, fill=color, width=width)  # Top-left
        draw.arc([x2 - radius * 2, y1, x2, y1 + radius * 2], 270, 0, fill=color, width=width)    # Top-right
        draw.arc([x1, y2 - radius * 2, x1 + radius * 2, y2], 90, 180, fill=color, width=width)   # Bottom-left
        draw.arc([x2 - radius * 2, y2 - radius * 2, x2, y2], 0, 90, fill=color, width=width)     # Bottom-right

    def _generate_qr_code(self, content, size):
        """
        Genera un código QR con el logo predeterminado en el centro
        
        Args:
            content (str): Contenido a codificar en el QR
            size (int): Tamaño final del QR en píxeles
        """
        # Configurar el QR con alta corrección de errores
        qr = qrcode.QRCode(
            version=None,
            error_correction=qrcode.constants.ERROR_CORRECT_H,
            box_size=10,
            border=1,
        )
        qr.add_data(content)
        qr.make(fit=True)
        
        # Crear imagen QR base
        qr_img = qr.make_image(fill_color="black", back_color="white")
        qr_img = qr_img.convert('RGBA')
        qr_img = qr_img.resize((size, size), Image.Resampling.LANCZOS)
        
        # Intentar usar el logo predeterminado
        try:
            if os.path.exists(self.QR_LOGO_PATH):
                # Abrir y procesar el logo predeterminado
                logo = Image.open(self.QR_LOGO_PATH)
                logo = logo.convert('RGBA')
                
                # Calcular el tamaño del logo (30% del tamaño del QR)
                logo_size = int(size * 0.30)
                logo = self._resize_image(logo, logo_size, logo_size)
                
                # Calcular posición para centrar el logo
                pos = ((size - logo_size) // 2, (size - logo_size) // 2)
                
                # Crear máscara circular para el logo
                mask = Image.new('L', (logo_size, logo_size), 0)
                draw = ImageDraw.Draw(mask)
                draw.ellipse((0, 0, logo_size, logo_size), fill=255)
                
                # Aplicar la máscara al logo
                output = qr_img.copy()
                output.paste(logo, pos, mask)
                return output
            else:
                logger.warning(f"No se encontró el archivo de logo predeterminado para QR en: {self.QR_LOGO_PATH}")
                return qr_img
        except Exception as e:
            logger.error(f"Error al procesar el logo para el QR: {str(e)}", exc_info=True)
            return qr_img

    def _resize_image(self, original_image, target_width, target_height):
        """Redimensiona una imagen manteniendo sus proporciones"""
        try:
            original_width, original_height = original_image.size
            ratio = min(target_width / original_width, target_height / original_height)
            new_width = int(original_width * ratio)
            new_height = int(original_height * ratio)
            resized = original_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            result = Image.new('RGBA', (target_width, target_height), (0, 0, 0, 0))
            x = (target_width - new_width) // 2
            y = (target_height - new_height) // 2
            result.paste(resized, (x, y), resized if resized.mode == 'RGBA' else None)
            return result
        except Exception as e:
            logger.error(f"Error al redimensionar imagen: {str(e)}", exc_info=True)
            raise

    def _calculate_required_text_width(self, draw, title, properties):
        """Método anterior mantenido para compatibilidad"""
        return self._calculate_text_width(
            draw, 
            title, 
            properties, 
            28,  # title_font_size
            18,  # normal_font_size
            16   # label_font_size
        )

    def _wrap_text(self, text, max_width, draw, font):
        """Divide un texto en múltiples líneas para que quepa en un ancho determinado"""
        if draw.textlength(text, font=font) <= max_width:
            return [text]
        
        words = text.split(' ')
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            
            if draw.textlength(test_line, font=font) <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                    current_line = ""
                
                if draw.textlength(word, font=font) > max_width:
                    i = 0
                    while i < len(word):
                        part = ""
                        while i < len(word) and draw.textlength(part + word[i], font=font) <= max_width:
                            part += word[i]
                            i += 1
                        lines.append(part)
                        if not part:
                            break
                else:
                    current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines

    def save_stamp_to_file(self, title, properties, qr_content, logo_image=None, filename=None, transparent_background=False):
        """
        Guarda la estampilla en un archivo
        
        Args:
            title (str): Título de la estampilla
            properties (dict): Diccionario de propiedades (clave-valor) a mostrar
            qr_content (str): Contenido a codificar en el QR
            logo_image (str, optional): Ruta a la imagen del logo. Por defecto None.
            filename (str, optional): Nombre del archivo de salida. Por defecto genera uno aleatorio.
            transparent_background (bool, optional): Si es True, el fondo será transparente. Por defecto False.
        """
        if filename is None:
            filename = f"estampilla_{uuid.uuid4().hex}.png"
        
        output = self.generate_stamp(title, properties, qr_content, logo_image, transparent_background)
        
        with open(filename, 'wb') as f:
            f.write(output.getvalue())
        
        return filename