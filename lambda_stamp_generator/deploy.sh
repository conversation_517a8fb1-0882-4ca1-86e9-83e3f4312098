#!/bin/bash

# Variables de configuración
AWS_REGION=${AWS_REGION:-"us-east-1"}
ECR_REPO_NAME="gedsys/gedsystool-stamp-generator"
LAMBDA_FUNCTION_NAME=${LAMBDA_FUNCTION_NAME:-"stamp-generator"}
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

echo "🎨 Iniciando despliegue de Lambda Stamp Generator..."
echo "Región: $AWS_REGION"
echo "Repositorio ECR: $ECR_REPO_NAME"
echo "Función Lambda: $LAMBDA_FUNCTION_NAME"
echo "Account ID: $AWS_ACCOUNT_ID"

# 1. Verificar si el repositorio ECR existe, si no, crearlo
echo "📦 Verificando repositorio ECR..."
aws ecr describe-repositories --repository-names $ECR_REPO_NAME --region $AWS_REGION 2>/dev/null || {
    echo "Creando repositorio ECR: $ECR_REPO_NAME"
    aws ecr create-repository \
        --repository-name $ECR_REPO_NAME \
        --region $AWS_REGION
}

# 2. Obtener login para ECR
echo "🔐 Obteniendo login para ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# 3. Build de la imagen Docker
echo "🏗️ Construyendo imagen Docker..."
docker build --platform linux/arm64 -t stamp-generator-lambda-temp .

# 4. Tag de la imagen
ECR_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPO_NAME:latest"
echo "🏷️ Taggeando imagen: $ECR_URI"
docker tag stamp-generator-lambda-temp:latest $ECR_URI

# 5. Push a ECR
echo "⬆️ Subiendo imagen a ECR..."
docker push $ECR_URI

# 6. Crear o actualizar función Lambda
echo "⚡ Desplegando función Lambda..."

# Verificar si la función existe
aws lambda get-function --function-name $LAMBDA_FUNCTION_NAME --region $AWS_REGION 2>/dev/null && {
    echo "Actualizando función Lambda existente..."
    aws lambda update-function-code \
        --function-name $LAMBDA_FUNCTION_NAME \
        --image-uri $ECR_URI \
        --region $AWS_REGION
    
    # Actualizar variables de entorno con valores dummy
    aws lambda update-function-configuration \
        --function-name $LAMBDA_FUNCTION_NAME \
        --environment "Variables={API_KEY=StampGenerator2024Key,S3_ENDPOINT_URL=https://ACTUALIZAR-CON-TU-ENDPOINT-S3.com,S3_ACCESS_KEY=ACTUALIZAR-CON-TU-ACCESS-KEY,S3_SECRET_KEY=ACTUALIZAR-CON-TU-SECRET-KEY,S3_REGION=us-east-1,STAMP_OUTPUT_DIR=radicados,LOGO_ASSETS_DIR=assets}" \
        --region $AWS_REGION
} || {
    echo "Creando nueva función Lambda..."
    
    # Crear rol IAM si no existe
    ROLE_NAME="lambda-stamp-generator-role"
    aws iam get-role --role-name $ROLE_NAME 2>/dev/null || {
        echo "Creando rol IAM para Lambda..."
        aws iam create-role \
            --role-name $ROLE_NAME \
            --assume-role-policy-document '{
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Principal": {
                            "Service": "lambda.amazonaws.com"
                        },
                        "Action": "sts:AssumeRole"
                    }
                ]
            }'
        
        # Adjuntar políticas necesarias
        aws iam attach-role-policy \
            --role-name $ROLE_NAME \
            --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        
        # Esperar a que el rol esté disponible
        echo "Esperando a que el rol esté disponible..."
        sleep 10
    }
    
    ROLE_ARN="arn:aws:iam::$AWS_ACCOUNT_ID:role/$ROLE_NAME"
    
    aws lambda create-function \
        --function-name $LAMBDA_FUNCTION_NAME \
        --package-type Image \
        --code ImageUri=$ECR_URI \
        --role $ROLE_ARN \
        --timeout 60 \
        --memory-size 512 \
        --architectures arm64 \
        --region $AWS_REGION \
        --environment "Variables={API_KEY=StampGenerator2024Key,S3_ENDPOINT_URL=https://ACTUALIZAR-CON-TU-ENDPOINT-S3.com,S3_ACCESS_KEY=ACTUALIZAR-CON-TU-ACCESS-KEY,S3_SECRET_KEY=ACTUALIZAR-CON-TU-SECRET-KEY,S3_REGION=us-east-1,STAMP_OUTPUT_DIR=radicados,LOGO_ASSETS_DIR=assets}"
}

# 7. Configurar Function URL si no existe
echo "🔗 Configurando Function URL..."
aws lambda get-function-url-config --function-name $LAMBDA_FUNCTION_NAME --region $AWS_REGION 2>/dev/null || {
    echo "Creando Function URL..."
    FUNCTION_URL=$(aws lambda create-function-url-config \
        --function-name $LAMBDA_FUNCTION_NAME \
        --auth-type NONE \
        --cors AllowCredentials=false,AllowMethods="POST",AllowOrigins="*",AllowHeaders="content-type,x-api-key,x-bucket-name" \
        --region $AWS_REGION \
        --query FunctionUrl --output text)
    
    echo "Function URL creada: $FUNCTION_URL"
}

# Obtener la Function URL actual
CURRENT_URL=$(aws lambda get-function-url-config \
    --function-name $LAMBDA_FUNCTION_NAME \
    --region $AWS_REGION \
    --query FunctionUrl --output text 2>/dev/null)

echo ""
echo "✅ Despliegue completado!"
echo "=================================="
echo "Función Lambda: $LAMBDA_FUNCTION_NAME"
echo "URI de imagen ECR: $ECR_URI"
if [ ! -z "$CURRENT_URL" ]; then
    echo "Function URL: $CURRENT_URL"
fi
echo ""
echo "⚠️  IMPORTANTE: Las variables de entorno S3 tienen valores DUMMY"
echo "=================================="
echo ""
echo "Para actualizar con tus credenciales reales, ejecuta:"
echo ""
echo "aws lambda update-function-configuration \\"
echo "  --function-name $LAMBDA_FUNCTION_NAME \\"
echo "  --environment \"Variables={API_KEY=StampGenerator2024Key,S3_ENDPOINT_URL=https://tu-servicio-s3-real.com,S3_ACCESS_KEY=tu-access-key-real,S3_SECRET_KEY=tu-secret-key-real,S3_REGION=us-east-1,STAMP_OUTPUT_DIR=radicados,LOGO_ASSETS_DIR=assets}\" \\"
echo "  --region $AWS_REGION"
echo ""
echo "=================================="
echo "📝 Ejemplo de prueba con cURL:"
echo "=================================="
echo ""
echo "# Prueba básica sin logo (no requiere S3 configurado)"
echo 'curl -X POST "'${CURRENT_URL:-'TU-FUNCTION-URL'}'" \'
echo '  -H "Content-Type: application/json" \'
echo '  -H "x-api-key: StampGenerator2024Key" \'
echo '  -H "x-bucket-name: mi-bucket-prueba" \'
echo '  -d '"'"'{
    "title": "Estampilla de Prueba",
    "properties": [
        {"label": "Documento", "value": "DOC-TEST-001"},
        {"label": "Fecha", "value": "'$(date +%Y-%m-%d)'"},
        {"label": "Estado", "value": "PRUEBA"}
    ],
    "qr_content": "https://ejemplo.com/verificar/test001",
    "output_filename": "test-stamp-'$(date +%s)'.png",
    "expiration_seconds": 3600,
    "transparent_background": false
}'"'"''
echo ""
echo "# Prueba con logo (requiere S3 configurado y logo existente)"
echo 'curl -X POST "'${CURRENT_URL:-'TU-FUNCTION-URL'}'" \'
echo '  -H "Content-Type: application/json" \'
echo '  -H "x-api-key: StampGenerator2024Key" \'
echo '  -H "x-bucket-name: mi-bucket-real" \'
echo '  -d '"'"'{
    "title": "Certificado Digital",
    "properties": [
        {"label": "ID", "value": "CERT-2024-001"},
        {"label": "Emisor", "value": "Sistema GEDSYS"},
        {"label": "Válido hasta", "value": "2025-01-15"}
    ],
    "qr_content": "https://gedsys.com/certificados/2024/001",
    "logo_filename": "logo-empresa.png",
    "output_filename": "certificado-2024-001.png",
    "expiration_seconds": 7200,
    "transparent_background": true
}'"'"''
echo ""
echo "=================================="
echo "🔍 Para ver los logs:"
echo "aws logs tail /aws/lambda/$LAMBDA_FUNCTION_NAME --follow"
echo "" 