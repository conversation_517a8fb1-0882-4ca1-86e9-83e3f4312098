# Lambda S3 Version Retriever

## 📋 Descripción

Lambda function para recuperar todas las versiones de un objeto específico en un bucket de S3 con versionado habilitado. Esta función está diseñada para trabajar con sistemas de gestión documental que requieren acceso al historial completo de versiones de archivos.

**Compatibilidad**: Funciona con AWS S3 y proveedores de almacenamiento compatibles con S3 como Contabo Storage, MinIO, DigitalOcean Spaces, etc.

## 🎯 Funcionalidad

- **Recuperación de versiones**: Obtiene todas las versiones de un archivo específico en S3
- **Información detallada**: Incluye ID de versión, fecha de modificación, tamaño y estado
- **Manejo de delete markers**: Identifica y reporta marcadores de eliminación
- **Numeración secuencial**: Asigna números de versión consecutivos a las versiones reales
- **Validación robusta**: Valida parámetros de entrada y maneja errores específicos

## 🏗️ Arquitectura

```
Cliente → API Gateway/Function URL → Lambda → S3 Bucket (con versionado)
```

## 📥 Formato de Entrada

### Headers Requeridos
- `Content-Type: application/json`
- `x-bucket-name: nombre-del-bucket`

### Body (JSON)
```json
{
  "filePath": "ruta/al/archivo.pdf"
}
```

## 📤 Formato de Respuesta

### Respuesta Exitosa (200)
```json
{
  "filePath": "documentos/archivo.pdf",
  "totalVersions": 3,
  "totalEntries": 4,
  "versions": [
    {
      "versionId": "version-id-1",
      "lastModified": "2024-01-01T10:00:00.000Z",
      "isDeleteMarker": false,
      "size": 1024,
      "number": 1
    },
    {
      "versionId": "version-id-2",
      "lastModified": "2024-01-02T10:00:00.000Z",
      "isDeleteMarker": false,
      "size": 2048,
      "number": 2
    },
    {
      "versionId": "delete-marker-id",
      "lastModified": "2024-01-03T10:00:00.000Z",
      "isDeleteMarker": true,
      "size": 0
    },
    {
      "versionId": "version-id-3",
      "lastModified": "2024-01-04T10:00:00.000Z",
      "isDeleteMarker": false,
      "size": 3072,
      "number": 3
    }
  ]
}
```

### Campos de Respuesta

- **filePath**: Ruta del archivo consultado
- **totalVersions**: Número total de versiones reales (sin delete markers)
- **totalEntries**: Número total de entradas (incluyendo delete markers)
- **versions**: Array de versiones ordenadas cronológicamente
  - **versionId**: ID único de la versión en S3
  - **lastModified**: Fecha de modificación en formato ISO 8601
  - **isDeleteMarker**: Indica si es un marcador de eliminación
  - **size**: Tamaño del archivo en bytes
  - **number**: Número secuencial de versión (solo para versiones reales)

## ❌ Códigos de Error

| Código | Descripción | Causa |
|--------|-------------|-------|
| 400 | Bad Request | Header faltante, JSON inválido, filePath vacío |
| 403 | Forbidden | Sin permisos para acceder al bucket/objeto |
| 404 | Not Found | Bucket no existe o archivo sin versiones |
| 500 | Internal Server Error | Error de configuración AWS o error inesperado |

### Ejemplos de Errores

```json
{
  "error": "Missing required header: x-bucket-name"
}
```

```json
{
  "error": "No versions found for object: archivo-inexistente.pdf"
}
```

## 🔧 Configuración

### Variables de Entorno Requeridas

```bash
S3_ACCESS_KEY_ID=tu_access_key
S3_SECRET_ACCESS_KEY=tu_secret_key
S3_REGION=us-east-1
S3_ENDPOINT_URL=https://usc1.contabostorage.com
```

**Nota**: Para AWS S3 estándar, omite `S3_ENDPOINT_URL` o déjalo vacío. Para proveedores S3-compatibles, configura la URL del endpoint correspondiente.

### Permisos IAM Requeridos

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:ListBucketVersions",
        "s3:GetBucketVersioning"
      ],
      "Resource": [
        "arn:aws:s3:::tu-bucket",
        "arn:aws:s3:::tu-bucket/*"
      ]
    }
  ]
}
```

## 🧪 Pruebas

### Ejecutar Script de Pruebas
```bash
./test_version_retriever.sh
```

### Prueba Manual con cURL
```bash
curl -X POST https://tu-function-url.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-bucket-name: tu-bucket" \
  -d '{
    "filePath": "documentos/archivo.pdf"
  }'
```

## 📋 Requisitos Previos

1. **Bucket S3 con versionado habilitado**:
   ```bash
   aws s3api put-bucket-versioning \
     --bucket tu-bucket \
     --versioning-configuration Status=Enabled
   ```

2. **Archivos con múltiples versiones** para pruebas

3. **Credenciales AWS configuradas** en la Lambda

## 🔍 Troubleshooting

### Problema: "No versions found"
- **Causa**: El archivo no existe o el bucket no tiene versionado habilitado
- **Solución**: Verificar que el archivo existe y que el versionado está activo

### Problema: "Access denied"
- **Causa**: Permisos IAM insuficientes
- **Solución**: Verificar que la Lambda tiene permisos `s3:ListBucketVersions`

### Problema: "AWS credentials not properly configured"
- **Causa**: Variables de entorno AWS no configuradas
- **Solución**: Configurar `S3_ACCESS_KEY_ID`, `S3_SECRET_ACCESS_KEY`, `S3_REGION` y `S3_ENDPOINT_URL` (para proveedores S3-compatibles)

## 📊 Información de Despliegue

- **Función**: ObjectStorageVersioningRetriever
- **ARN**: `arn:aws:lambda:us-east-1:165354057769:function:ObjectStorageVersioningRetriever`
- **Región**: us-east-1
- **Runtime**: Python 3.x
- **Arquitectura**: x86_64 o arm64

## 🔄 Mejoras Implementadas

### ✅ Correcciones Aplicadas

1. **Serialización JSON**: Manejo correcto de objetos datetime
2. **Consistencia de headers**: Uso consistente de `x-bucket-name`
3. **Numeración de versiones**: Lógica corregida para numeración secuencial
4. **Información enriquecida**: Respuesta con fechas, tamaños y contadores
5. **Validación robusta**: Validación de entrada y manejo de errores mejorado
6. **Variables de entorno**: Uso de nombres estándar de AWS

### 🎯 Características Destacadas

- **Paginación automática**: Maneja buckets con gran cantidad de versiones
- **Ordenamiento cronológico**: Versiones ordenadas de más antigua a más reciente
- **Información completa**: Incluye metadatos útiles para cada versión
- **Manejo de delete markers**: Identifica y reporta eliminaciones lógicas
- **Headers HTTP apropiados**: Respuestas con Content-Type correcto

## 📝 Notas Importantes

1. El bucket **debe** tener versionado habilitado para que la función opere correctamente
2. Los delete markers aparecen en la lista pero no tienen número de versión asignado
3. Las versiones se ordenan cronológicamente (más antigua primero)
4. La numeración de versiones es secuencial y solo se aplica a versiones reales
5. El tamaño se reporta en bytes para versiones reales y 0 para delete markers 