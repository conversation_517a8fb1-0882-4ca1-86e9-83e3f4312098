#!/bin/bash

# Script para actualizar la Lambda ObjectStorageVersioningRetriever existente
# ARN: arn:aws:lambda:us-east-1:165354057769:function:ObjectStorageVersioningRetriever

set -e

FUNCTION_NAME="ObjectStorageVersioningRetriever"
REGION="us-east-1"

echo "🚀 Actualizando Lambda: $FUNCTION_NAME"
echo "📍 Región: $REGION"
echo ""

# Verificar que la función existe
echo "🔍 Verificando que la función Lambda existe..."
if aws lambda get-function --function-name "$FUNCTION_NAME" --region "$REGION" >/dev/null 2>&1; then
    echo "✅ Función encontrada: $FUNCTION_NAME"
else
    echo "❌ Error: La función $FUNCTION_NAME no existe en la región $REGION"
    exit 1
fi

# Crear archivo ZIP con el código actualizado
echo ""
echo "📦 Creando paquete de despliegue..."
rm -f lambda_function.zip
zip lambda_function.zip lambda_function.py

if [ ! -f "lambda_function.zip" ]; then
    echo "❌ Error: No se pudo crear el archivo ZIP"
    exit 1
fi

echo "✅ Paquete creado: lambda_function.zip"

# Actualizar el código de la función
echo ""
echo "🔄 Actualizando código de la función Lambda..."
aws lambda update-function-code \
    --function-name "$FUNCTION_NAME" \
    --zip-file fileb://lambda_function.zip \
    --region "$REGION"

if [ $? -eq 0 ]; then
    echo "✅ Código actualizado exitosamente"
else
    echo "❌ Error al actualizar el código"
    exit 1
fi

# Actualizar variables de entorno (usar nombres estándar de AWS)
echo ""
echo "🔧 Actualizando variables de entorno..."

# Crear archivo temporal con las variables de entorno
cat > env_vars.json << 'EOF'
{
  "Variables": {
    "S3_ACCESS_KEY_ID": "ACTUALIZAR_CON_TU_ACCESS_KEY",
    "S3_SECRET_ACCESS_KEY": "ACTUALIZAR_CON_TU_SECRET_KEY",
    "S3_REGION": "us-east-1",
    "S3_ENDPOINT_URL": "https://usc1.contabostorage.com"
  }
}
EOF

aws lambda update-function-configuration \
    --function-name "$FUNCTION_NAME" \
    --environment file://env_vars.json \
    --region "$REGION"

# Limpiar archivo temporal
rm -f env_vars.json

if [ $? -eq 0 ]; then
    echo "✅ Variables de entorno actualizadas"
    echo "⚠️  IMPORTANTE: Actualiza las credenciales AWS en la consola de Lambda"
else
    echo "❌ Error al actualizar variables de entorno"
fi

# Obtener información actualizada de la función
echo ""
echo "📊 Información de la función actualizada:"
aws lambda get-function \
    --function-name "$FUNCTION_NAME" \
    --region "$REGION" \
    --query '{
        FunctionName: Configuration.FunctionName,
        Runtime: Configuration.Runtime,
        LastModified: Configuration.LastModified,
        CodeSize: Configuration.CodeSize,
        State: Configuration.State,
        Version: Configuration.Version
    }' \
    --output table

# Verificar si tiene Function URL configurada
echo ""
echo "🔗 Verificando Function URL..."
FUNCTION_URL=$(aws lambda get-function-url-config \
    --function-name "$FUNCTION_NAME" \
    --region "$REGION" \
    --query 'FunctionUrl' \
    --output text 2>/dev/null || echo "No configurada")

if [ "$FUNCTION_URL" != "No configurada" ]; then
    echo "✅ Function URL activa: $FUNCTION_URL"
else
    echo "⚠️  Function URL no configurada"
    echo "💡 Para crear una Function URL, ejecuta:"
    echo "   aws lambda create-function-url-config --function-name $FUNCTION_NAME --auth-type NONE --region $REGION"
fi

# Limpiar archivos temporales
echo ""
echo "🧹 Limpiando archivos temporales..."
rm -f lambda_function.zip env_vars.json
echo "✅ Limpieza completada"

echo ""
echo "🎉 Actualización completada exitosamente!"
echo ""
echo "📋 Próximos pasos:"
echo "1. Actualizar las credenciales AWS en las variables de entorno de la Lambda"
echo "2. Probar la función con el script: ./test_version_retriever.sh"
echo "3. Verificar que el bucket S3 tenga versionado habilitado"
echo ""
echo "📖 Para más información, consulta el README.md" 