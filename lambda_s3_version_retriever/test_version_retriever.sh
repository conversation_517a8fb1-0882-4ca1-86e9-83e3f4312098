#!/bin/bash

# Configuración de la Lambda ObjectStorageVersioningRetriever
FUNCTION_URL="https://tu-function-url.lambda-url.us-east-1.on.aws/"
BUCKET_NAME="tu-bucket-name"

echo "=== Pruebas para Lambda ObjectStorageVersioningRetriever ==="
echo "Function URL: $FUNCTION_URL"
echo "Bucket: $BUCKET_NAME"
echo ""

echo "1. Prueba básica - Obtener versiones de un archivo:"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-bucket-name: $BUCKET_NAME" \
  -d '{
    "filePath": "documentos/archivo_ejemplo.pdf"
  }'

echo ""
echo ""
echo "2. Prueba con archivo en subdirectorio:"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-bucket-name: $BUCKET_NAME" \
  -d '{
    "filePath": "carpeta/subcarpeta/documento.pdf"
  }'

echo ""
echo ""
echo "3. Prueba de error - Sin header de bucket:"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "filePath": "documentos/archivo.pdf"
  }'

echo ""
echo ""
echo "4. Prueba de error - Sin filePath en body:"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-bucket-name: $BUCKET_NAME" \
  -d '{
    "otherField": "valor"
  }'

echo ""
echo ""
echo "5. Prueba de error - filePath vacío:"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-bucket-name: $BUCKET_NAME" \
  -d '{
    "filePath": ""
  }'

echo ""
echo ""
echo "6. Prueba de error - JSON inválido:"
echo ""
curl -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "x-bucket-name: $BUCKET_NAME" \
  -d '{"filePath": "archivo.pdf"'

echo ""
echo ""
echo "=== Información de la Lambda ==="
echo "- Función: ObjectStorageVersioningRetriever"
echo "- ARN: arn:aws:lambda:us-east-1:165354057769:function:ObjectStorageVersioningRetriever"
echo "- Región: us-east-1"
echo ""
echo "=== Formato de respuesta esperado ==="
cat << 'EOF'
{
  "filePath": "documentos/archivo.pdf",
  "totalVersions": 3,
  "totalEntries": 4,
  "versions": [
    {
      "versionId": "version-id-1",
      "lastModified": "2024-01-01T10:00:00.000Z",
      "isDeleteMarker": false,
      "size": 1024,
      "number": 1
    },
    {
      "versionId": "version-id-2", 
      "lastModified": "2024-01-02T10:00:00.000Z",
      "isDeleteMarker": false,
      "size": 2048,
      "number": 2
    },
    {
      "versionId": "delete-marker-id",
      "lastModified": "2024-01-03T10:00:00.000Z", 
      "isDeleteMarker": true,
      "size": 0
    },
    {
      "versionId": "version-id-3",
      "lastModified": "2024-01-04T10:00:00.000Z",
      "isDeleteMarker": false,
      "size": 3072,
      "number": 3
    }
  ]
}
EOF

echo ""
echo ""
echo "=== Comandos cURL individuales ==="
echo ""
echo "Comando básico:"
cat << 'EOF'
curl -X POST https://tu-function-url.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-bucket-name: tu-bucket" \
  -d '{
    "filePath": "ruta/al/archivo.pdf"
  }'
EOF

echo ""
echo ""
echo "⚠️  Notas importantes:"
echo "1. Actualiza FUNCTION_URL con la URL real de tu Lambda"
echo "2. Actualiza BUCKET_NAME con el nombre de tu bucket"
echo "3. Asegúrate de que las variables de entorno estén configuradas en la Lambda:"
echo "   - S3_ACCESS_KEY_ID"
echo "   - S3_SECRET_ACCESS_KEY" 
echo "   - S3_REGION"
echo "   - S3_ENDPOINT_URL (para proveedores S3-compatibles como Contabo Storage)"
echo "4. El bucket debe tener versionado habilitado para que funcione correctamente" 