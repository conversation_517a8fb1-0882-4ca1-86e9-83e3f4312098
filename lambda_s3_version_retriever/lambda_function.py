import json
import os
from datetime import datetime
from typing import Dict, List, Any
import boto3
from botocore.config import Config
from botocore.exceptions import ClientError, NoCredentialsError, ParamValidationError


class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder for datetime objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Lambda function to retrieve all versions of an S3 object.
    
    Args:
        event: Lambda event with headers and body
        context: Lambda context object
        
    Returns:
        Response with list of versions or error message
    """
    
    try:
        # Extract bucket name from header
        headers = event.get('headers', {})
        bucket_name = headers.get('x-bucket-name')
        
        if not bucket_name:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    'error': 'Missing required header: x-bucket-name'
                })
            }
        
        # Extract file path from request body
        try:
            body = json.loads(event.get('body', '{}'))
            file_path = body.get('filePath')
        except json.JSONDecodeError:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    'error': 'Invalid JSON in request body'
                })
            }
        
        if not file_path:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    'error': 'Missing required field in body: filePath'
                })
            }
        
        # Validate file path
        if not file_path.strip():
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    'error': 'filePath cannot be empty'
                })
            }
        
        # Configure S3 client with path-style addressing for third-party compatibility
        s3_config = Config(
            s3={'addressing_style': 'path'},
            signature_version='s3v4'
        )
        
        # Create S3 client with environment variables for custom S3-compatible storage
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.environ.get('S3_ACCESS_KEY_ID'),
            aws_secret_access_key=os.environ.get('S3_SECRET_ACCESS_KEY'),
            region_name=os.environ.get('S3_REGION', 'us-east-1'),
            endpoint_url=os.environ.get('S3_ENDPOINT_URL'),
            config=s3_config
        )
        
        # Retrieve all versions of the object
        versions_list: List[Dict[str, Any]] = []
        
        # Paginate through all versions
        paginator = s3_client.get_paginator('list_object_versions')
        page_iterator = paginator.paginate(
            Bucket=bucket_name,
            Prefix=file_path
        )
        
        # Collect all versions
        for page in page_iterator:
            # Process regular versions
            for version in page.get('Versions', []):
                if version['Key'] == file_path:
                    versions_list.append({
                        'VersionId': version['VersionId'],
                        'LastModified': version['LastModified'],
                        'IsDeleteMarker': False,
                        'Size': version.get('Size', 0)
                    })
            
            # Process delete markers if any
            for delete_marker in page.get('DeleteMarkers', []):
                if delete_marker['Key'] == file_path:
                    versions_list.append({
                        'VersionId': delete_marker['VersionId'],
                        'LastModified': delete_marker['LastModified'],
                        'IsDeleteMarker': True,
                        'Size': 0
                    })
        
        # Check if any versions were found
        if not versions_list:
            return {
                'statusCode': 404,
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    'error': f'No versions found for object: {file_path}'
                })
            }
        
        # Sort versions from oldest to newest
        versions_list.sort(key=lambda x: x['LastModified'])
        
        # Format response with sequential numbering for all versions
        response_versions = []
        actual_version_number = 1
        
        for version in versions_list:
            version_info = {
                'versionId': version['VersionId'],
                'lastModified': version['LastModified'].isoformat(),
                'isDeleteMarker': version['IsDeleteMarker'],
                'size': version['Size']
            }
            
            # Only assign version numbers to actual file versions (not delete markers)
            if not version['IsDeleteMarker']:
                version_info['number'] = actual_version_number
                actual_version_number += 1
            
            response_versions.append(version_info)
        
        # Return successful response
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'filePath': file_path,
                'totalVersions': len([v for v in response_versions if not v['isDeleteMarker']]),
                'totalEntries': len(response_versions),
                'versions': response_versions
            }, cls=DateTimeEncoder)
        }
        
    except NoCredentialsError:
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'error': 'AWS credentials not properly configured'
            })
        }
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        
        # Handle specific S3 errors
        if error_code == 'NoSuchBucket':
            return {
                'statusCode': 404,
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    'error': f'Bucket not found: {bucket_name}'
                })
            }
        elif error_code == 'AccessDenied':
            return {
                'statusCode': 403,
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    'error': 'Access denied to bucket or object'
                })
            }
        else:
            return {
                'statusCode': 500,
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({
                    'error': f'S3 error: {str(e)}'
                })
            }
            
    except ParamValidationError as e:
        return {
            'statusCode': 400,
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'error': f'Invalid parameters: {str(e)}'
            })
        }
        
    except Exception as e:
        # Catch-all for unexpected errors
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'error': f'Unexpected error: {str(e)}'
            })
        }
