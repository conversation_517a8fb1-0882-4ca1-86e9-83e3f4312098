import json
import os
import tempfile
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
import boto3
from botocore.config import Config
from botocore.exceptions import ClientError

# Importar la implementación real del DocxReplacer
from docx_replacer import DocxReplacer

class S3DocxClient:
    def __init__(self):
        # Configurar path-style addressing para object storage S3-compatible
        config = Config(
            s3={
                'addressing_style': 'path'
            }
        )
        
        # Configuración igual que el proyecto de referencia
        self.s3_client = boto3.client(
            's3',
            endpoint_url=os.getenv('S3_ENDPOINT_URL'),
            aws_access_key_id=os.getenv('S3_ACCESS_KEY'),
            aws_secret_access_key=os.getenv('S3_SECRET_KEY'),
            region_name=os.getenv('S3_REGION', 'us-east-1'),
            config=config
        )
    
    def download_docx(self, bucket_name: str, docx_filename: str) -> str:
        """
        Descarga documento DOCX desde S3: {bucket}/borradores/{docx_filename}
        
        Args:
            bucket_name: Nombre del bucket
            docx_filename: Nombre del archivo DOCX
            
        Returns:
            Path del archivo temporal descargado
        """
        try:
            docx_input_dir = os.getenv('DOCX_INPUT_DIR', 'borradores')
            s3_key = f"{docx_input_dir}/{docx_filename}"
            
            # Crear archivo temporal
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
            temp_file.close()
            
            # Descargar desde S3
            self.s3_client.download_file(bucket_name, s3_key, temp_file.name)
            
            return temp_file.name
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                raise ValueError(f"Documento no encontrado en S3: {s3_key}")
            else:
                raise ValueError(f"Error de cliente S3 al descargar documento: {str(e)}")
        except Exception as e:
            raise ValueError(f"Error inesperado al descargar documento: {str(e)}")
    
    def upload_pdf(self, bucket_name: str, pdf_path: str, output_filename: str, is_temporal: bool = False) -> str:
        """
        Sube documento PDF procesado a S3: {bucket}/documentos/{output_filename} o {bucket}/temporal/{output_filename}
        
        Args:
            bucket_name: Nombre del bucket
            pdf_path: Path del archivo PDF local
            output_filename: Nombre del archivo de salida
            is_temporal: Si True, guarda en directorio temporal
            
        Returns:
            S3 key del archivo subido
        """
        try:
            if is_temporal:
                pdf_output_dir = os.getenv('PDF_TEMPORAL_DIR', 'temporal')
            else:
                pdf_output_dir = os.getenv('PDF_OUTPUT_DIR', 'documentos')
            
            s3_key = f"{pdf_output_dir}/{output_filename}"
            
            # Subir a S3
            self.s3_client.upload_file(
                pdf_path, 
                bucket_name, 
                s3_key,
                ExtraArgs={'ContentType': 'application/pdf'}
            )
            
            return s3_key
            
        except ClientError as e:
            raise ValueError(f"Error de cliente S3 al subir documento: {str(e)}")
        except Exception as e:
            raise ValueError(f"Error inesperado al subir documento: {str(e)}")
    
    def generate_presigned_url(self, bucket_name: str, s3_key: str, expiration_seconds: int) -> Optional[str]:
        """
        Genera URL prefirmada para descarga
        
        Args:
            bucket_name: Nombre del bucket
            s3_key: Clave del objeto en S3
            expiration_seconds: Tiempo de expiración en segundos
            
        Returns:
            URL prefirmada o None si hay error
        """
        try:
            if expiration_seconds <= 0:
                return None
                
            url = self.s3_client.generate_presigned_url(
                ClientMethod='get_object',
                Params={
                    'Bucket': bucket_name,
                    'Key': s3_key
                },
                ExpiresIn=expiration_seconds
            )
            
            return url
            
        except ClientError as e:
            print(f"Error de cliente S3 al generar URL prefirmada: {e}")
            return None
        except Exception as e:
            print(f"Error inesperado al generar URL prefirmada: {e}")
            return None

def authenticate_request(headers: Dict[str, str]) -> bool:
    """
    Valida el API key desde los headers
    
    Args:
        headers: Headers de la request
        
    Returns:
        True si la autenticación es exitosa
    """
    api_key = os.getenv('API_KEY', 'DocxReplacer2024Key')  # Clave por defecto
    
    # Buscar el header x-api-key (case insensitive)
    request_api_key = None
    for key, value in headers.items():
        if key.lower() == 'x-api-key':
            request_api_key = value
            break
    
    if not request_api_key:
        return False
    
    return request_api_key == api_key

def parse_event(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parsea el evento para soportar tanto Lambda tradicional como Function URL
    Extrae bucket_name desde headers para mayor seguridad
    """
    # Detectar si es un evento de Function URL (HTTP request)
    if 'httpMethod' in event or 'requestContext' in event:
        # Es un evento de Function URL
        headers = event.get('headers', {})
        
        # Autenticar la request
        if not authenticate_request(headers):
            raise ValueError("API key inválido o faltante")
        
        # Extraer bucket_name desde headers
        bucket_name = None
        for key, value in headers.items():
            if key.lower() == 'x-bucket-name':
                bucket_name = value
                break
        
        if not bucket_name:
            raise ValueError("Header 'x-bucket-name' requerido")
        
        # Parsear body para otros parámetros
        parsed_data = {'bucket_name': bucket_name}
        
        try:
            if 'body' in event and event['body']:
                # El body puede venir como string, parsearlo
                if isinstance(event['body'], str):
                    body = json.loads(event['body'])
                else:
                    body = event['body']
                parsed_data.update(body)
            else:
                # Si no hay body, buscar en queryStringParameters
                query_params = event.get('queryStringParameters', {})
                if query_params:
                    parsed_data.update(query_params)
        except json.JSONDecodeError:
            raise ValueError("Body JSON inválido")
            
        return parsed_data
    else:
        # Es un evento de Lambda tradicional
        # Para backward compatibility, usar API key desde event si está presente
        if 'api_key' in event:
            api_key = os.getenv('API_KEY', 'DocxReplacer2024Key')
            if event['api_key'] != api_key:
                raise ValueError("API key inválido")
        
        return event

def validate_filename(filename: str, file_type: str) -> None:
    """
    Valida que el nombre de archivo tenga la extensión correcta
    
    Args:
        filename: Nombre del archivo a validar
        file_type: Tipo de archivo esperado ('docx' o 'pdf')
    """
    if file_type == 'docx' and not filename.lower().endswith('.docx'):
        raise ValueError(f"El archivo '{filename}' debe tener extensión .docx")
    elif file_type == 'pdf' and not filename.lower().endswith('.pdf'):
        raise ValueError(f"El archivo '{filename}' debe tener extensión .pdf")

def replace_docx_service(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Servicio principal para procesar documentos DOCX con reemplazos y generar PDF
    
    Args:
        request_data: Datos de la request con los parámetros necesarios
        
    Returns:
        Diccionario con el resultado del procesamiento
    """
    # Validar parámetros requeridos
    required_fields = ['bucket_name', 'docx_filename', 'replacements']
    missing_params = [param for param in required_fields if param not in request_data]
    
    if missing_params:
        raise ValueError(f'Parámetros faltantes: {", ".join(missing_params)}')
    
    bucket_name = request_data['bucket_name']
    docx_filename = request_data['docx_filename']
    replacements = request_data['replacements']
    temporal = request_data.get('temporal', False)  # False por defecto
    expiration_seconds = request_data.get('expiration_seconds', 3600)  # 1 hora por defecto
    
    # Generar nombre del archivo de salida basado en docx_filename
    base_name = os.path.splitext(docx_filename)[0]
    output_filename = f"{base_name}.pdf"
    
    # Validar tipos de datos
    if not isinstance(replacements, dict):
        raise ValueError("El campo 'replacements' debe ser un diccionario")
    
    if not isinstance(temporal, bool):
        raise ValueError("El campo 'temporal' debe ser un booleano")
    
    if not isinstance(expiration_seconds, int) or expiration_seconds < 0:
        raise ValueError("El campo 'expiration_seconds' debe ser un entero no negativo")
    
    # Validar nombres de archivos
    validate_filename(docx_filename, 'docx')
    
    # Inicializar cliente S3
    s3_client = S3DocxClient()
    
    # Variables para limpieza
    temp_input_file = None
    temp_pdf_file = None
    
    try:
        # 1. Descargar documento desde S3
        print(f"Descargando documento: {docx_filename} desde bucket: {bucket_name}")
        temp_input_file = s3_client.download_docx(bucket_name, docx_filename)
        
        # 2. Procesar documento con reemplazos y convertir a PDF
        print(f"Procesando documento con {len(replacements)} reemplazos")
        replacer = DocxReplacer()
        temp_pdf_file = replacer.replace_and_convert_to_pdf(temp_input_file, replacements, output_filename)
        
        # 3. Subir documento PDF procesado a S3
        storage_location = "temporal" if temporal else "documentos"
        print(f"Subiendo documento PDF procesado: {output_filename} a {storage_location}")
        s3_key = s3_client.upload_pdf(bucket_name, temp_pdf_file, output_filename, temporal)
        
        # 4. Generar URL prefirmada si se solicita
        presigned_url = None
        if expiration_seconds > 0:
            print(f"Generando URL prefirmada con expiración de {expiration_seconds} segundos")
            presigned_url = s3_client.generate_presigned_url(bucket_name, s3_key, expiration_seconds)
        
        # 5. Preparar respuesta
        response = {
            's3_key': s3_key,
            'temporal': temporal,
            'timestamp': datetime.utcnow().isoformat() + 'Z'
        }
        
        if presigned_url:
            response['presigned_url'] = presigned_url
            response['expires_in_seconds'] = expiration_seconds
        
        print(f"Procesamiento completado exitosamente: {output_filename}")
        return response
        
    except Exception as e:
        error_msg = f"Error al procesar documento: {str(e)}"
        print(error_msg)
        raise ValueError(error_msg)
        
    finally:
        # Limpiar archivos temporales
        for temp_file in [temp_input_file, temp_pdf_file]:
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                except Exception as cleanup_error:
                    print(f"Error al limpiar archivo temporal {temp_file}: {cleanup_error}")

def lambda_handler(event: Dict[str, Any], context) -> Dict[str, Any]:
    """
    Handler principal de la función Lambda
    
    Args:
        event: Evento de Lambda (puede ser Function URL o invocación directa)
        context: Contexto de Lambda
        
    Returns:
        Respuesta HTTP para Function URL o respuesta directa para Lambda
    """
    try:
        # Parsear evento
        request_data = parse_event(event)
        # Procesar documento
        result = replace_docx_service(request_data)
        
        # Determinar formato de respuesta
        if 'httpMethod' in event or 'requestContext' in event:
            # Respuesta para Function URL
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type, x-api-key, x-bucket-name',
                    'Access-Control-Allow-Methods': 'POST, OPTIONS'
                },
                'body': json.dumps(result, ensure_ascii=False)
            }
        else:
            # Respuesta para Lambda tradicional
            return result
            
    except ValueError as ve:
        error_response = {
            'success': False,
            'error': 'Validation Error',
            'message': str(ve),
            'timestamp': datetime.utcnow().isoformat() + 'Z'
        }
        
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps(error_response, ensure_ascii=False)
            }
        else:
            return error_response
            
    except Exception as e:
        error_response = {
            'success': False,
            'error': 'Internal Server Error',
            'message': f'Error interno del servidor: {str(e)}',
            'timestamp': datetime.utcnow().isoformat() + 'Z'
        }
        
        print(f"Error interno: {str(e)}")
        
        if 'httpMethod' in event or 'requestContext' in event:
            return {
                'statusCode': 500,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps(error_response, ensure_ascii=False)
            }
        else:
            return error_response 