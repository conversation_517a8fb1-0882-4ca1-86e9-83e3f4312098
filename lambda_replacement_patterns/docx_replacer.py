import os
import tempfile
import subprocess
from datetime import datetime
from typing import Dict
from docx import Document
from python_docx_replace import docx_replace

class DocxReplacer:
    """
    Clase para procesar documentos DOCX con reemplazos de texto
    Basada en la funcionalidad del proyecto gedsystool_replacer
    """
    
    def __init__(self):
        """Inicializar el procesador de documentos DOCX"""
        pass
    
    def replace_docx(self, docx_path: str, replacements: Dict[str, str], output_filename: str) -> str:
        """
        Procesa un documento DOCX aplicando reemplazos de texto
        
        Args:
            docx_path: Path del archivo DOCX de entrada
            replacements: Diccionario con los reemplazos a aplicar {clave: valor}
            output_filename: Nombre del archivo de salida
            
        Returns:
            Path del archivo temporal procesado
            
        Raises:
            FileNotFoundError: Si el archivo de entrada no existe
            ValueError: Si hay errores en el procesamiento
        """
        if not os.path.exists(docx_path):
            raise FileNotFoundError(f"El archivo {docx_path} no existe")
        
        try:
            # Cargar el documento
            doc = Document(docx_path)
            
            # Aplicar reemplazos usando python-docx-replace
            docx_replace(doc, **replacements)
            
            # Generar timestamp para el archivo de salida
            timestamp = datetime.now().strftime("%d%m%Y%H%M%S")
            
            # Extraer nombre base sin extensión
            base_name = os.path.splitext(output_filename)[0]
            
            # Crear nombre de archivo único con timestamp
            unique_filename = f"{base_name}_{timestamp}.docx"
            
            # Crear archivo temporal para la salida
            temp_output = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
            temp_output.close()
            
            # Guardar el documento procesado
            doc.save(temp_output.name)
            
            return temp_output.name
            
        except Exception as e:
            raise ValueError(f"Error al procesar el documento DOCX: {str(e)}")
    
    def replace_and_convert_to_pdf(self, docx_path: str, replacements: Dict[str, str], output_filename: str) -> str:
        """
        Procesa un documento DOCX aplicando reemplazos de texto y lo convierte a PDF
        
        Args:
            docx_path: Path del archivo DOCX de entrada
            replacements: Diccionario con los reemplazos a aplicar {clave: valor}
            output_filename: Nombre del archivo de salida (debe terminar en .pdf)
            
        Returns:
            Path del archivo PDF temporal procesado
            
        Raises:
            FileNotFoundError: Si el archivo de entrada no existe
            ValueError: Si hay errores en el procesamiento
        """
        if not os.path.exists(docx_path):
            raise FileNotFoundError(f"El archivo {docx_path} no existe")
        
        try:
            # Cargar el documento
            doc = Document(docx_path)
            
            # Aplicar reemplazos usando python-docx-replace
            docx_replace(doc, **replacements)
            
            # Generar timestamp para el archivo de salida
            timestamp = datetime.now().strftime("%d%m%Y%H%M%S")
            
            # Extraer nombre base sin extensión
            base_name = os.path.splitext(output_filename)[0]
            
            # Crear nombre de archivo único con timestamp
            unique_filename = f"{base_name}_{timestamp}"
            
            # Crear archivo temporal para el DOCX procesado
            temp_docx = tempfile.NamedTemporaryFile(delete=False, suffix='.docx')
            temp_docx.close()
            
            # Guardar el documento procesado
            doc.save(temp_docx.name)
            
            # Crear archivo temporal para el PDF
            temp_pdf = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            temp_pdf.close()
            
            # Convertir DOCX a PDF usando LibreOffice
            self._convert_docx_to_pdf(temp_docx.name, temp_pdf.name)
            
            # Limpiar el archivo DOCX temporal
            try:
                os.unlink(temp_docx.name)
            except Exception as cleanup_error:
                print(f"Error al limpiar archivo DOCX temporal: {cleanup_error}")
            
            return temp_pdf.name
            
        except Exception as e:
            raise ValueError(f"Error al procesar el documento DOCX y convertir a PDF: {str(e)}")
    
    def _convert_docx_to_pdf(self, docx_path: str, pdf_path: str) -> None:
        """
        Convierte un archivo DOCX a PDF usando LibreOffice
        
        Args:
            docx_path: Path del archivo DOCX de entrada
            pdf_path: Path del archivo PDF de salida
        """
        temp_dir = None
        try:
            # Crear directorio temporal para la conversión
            temp_dir = tempfile.mkdtemp()
            if not os.path.exists(docx_path):
                raise ValueError(f"Archivo DOCX no encontrado: {docx_path}")
            # Ejecutar LibreOffice para convertir DOCX a PDF
            libreoffice_path = "/usr/local/bin/libreoffice"
            if not os.path.exists(libreoffice_path):
                # Intentar rutas alternativas
                alternative_paths = [
                    "/opt/libreoffice7.6/program/soffice",
                    "/usr/bin/libreoffice",
                    "libreoffice"  # Como fallback
                ]

                libreoffice_path = None
                for path in alternative_paths:
                    if os.path.exists(path) or path == "libreoffice":
                        libreoffice_path = path
                        break
                    
                if not libreoffice_path:
                    raise ValueError("LibreOffice no encontrado en el sistema")
            env = os.environ.copy()
            env.update({
                'HOME': temp_dir,  # Usar temp_dir como HOME
                'SAL_USE_VCLPLUGIN': 'svp',
                'DISPLAY': ':99'
            })
            cmd = [
                libreoffice_path,
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', temp_dir,
                '--invisible',
                '--nodefault',
                '--nolockcheck',
                '--nologo',
                '--norestore',
                docx_path
            ]   
            
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True,
                timeout=60,
                cwd=temp_dir
            )
            if result.returncode != 0:
                raise ValueError(f"Error en conversión LibreOffice: {result.stderr}")
            
            # Encontrar el archivo PDF generado
            docx_basename = os.path.splitext(os.path.basename(docx_path))[0]
            generated_pdf = os.path.join(temp_dir, f"{docx_basename}.pdf")
            
            if not os.path.exists(generated_pdf):
                raise ValueError("No se pudo generar el archivo PDF")
            
            # Mover el PDF al destino final
            import shutil
            shutil.move(generated_pdf, pdf_path)
            
            # Limpiar directorio temporal
            try:
                shutil.rmtree(temp_dir)
            except Exception as cleanup_error:
                print(f"Error al limpiar directorio temporal: {cleanup_error}")
                
        except subprocess.TimeoutExpired:
            raise ValueError("Timeout en la conversión a PDF")
        except Exception as e:
            raise ValueError(f"Error al convertir DOCX a PDF: {str(e)}")
    
    def validate_replacements(self, replacements: Dict[str, str]) -> bool:
        """
        Valida que el diccionario de reemplazos sea válido
        
        Args:
            replacements: Diccionario con los reemplazos
            
        Returns:
            True si es válido
            
        Raises:
            ValueError: Si hay errores de validación
        """
        if not isinstance(replacements, dict):
            raise ValueError("Los reemplazos deben ser un diccionario")
        
        if not replacements:
            raise ValueError("Debe proporcionar al menos un reemplazo")
        
        # Validar que todas las claves y valores sean strings
        for key, value in replacements.items():
            if not isinstance(key, str):
                raise ValueError(f"La clave '{key}' debe ser una cadena de texto")
            if not isinstance(value, str):
                raise ValueError(f"El valor para la clave '{key}' debe ser una cadena de texto")
            if not key.strip():
                raise ValueError("Las claves no pueden estar vacías")
        
        return True
    
    def get_document_info(self, docx_path: str) -> Dict[str, any]:
        """
        Obtiene información básica del documento DOCX
        
        Args:
            docx_path: Path del archivo DOCX
            
        Returns:
            Diccionario con información del documento
        """
        if not os.path.exists(docx_path):
            raise FileNotFoundError(f"El archivo {docx_path} no existe")
        
        try:
            doc = Document(docx_path)
            
            # Contar párrafos y tablas
            paragraph_count = len(doc.paragraphs)
            table_count = len(doc.tables)
            
            # Obtener propiedades del documento
            core_props = doc.core_properties
            
            info = {
                'paragraph_count': paragraph_count,
                'table_count': table_count,
                'title': core_props.title or 'Sin título',
                'author': core_props.author or 'Sin autor',
                'created': core_props.created.isoformat() if core_props.created else None,
                'modified': core_props.modified.isoformat() if core_props.modified else None,
                'file_size_bytes': os.path.getsize(docx_path)
            }
            
            return info
            
        except Exception as e:
            raise ValueError(f"Error al obtener información del documento: {str(e)}")
    
    def preview_replacements(self, docx_path: str, replacements: Dict[str, str]) -> Dict[str, any]:
        """
        Previsualiza qué reemplazos se aplicarían sin modificar el documento
        
        Args:
            docx_path: Path del archivo DOCX
            replacements: Diccionario con los reemplazos
            
        Returns:
            Diccionario con información de la previsualización
        """
        if not os.path.exists(docx_path):
            raise FileNotFoundError(f"El archivo {docx_path} no existe")
        
        self.validate_replacements(replacements)
        
        try:
            doc = Document(docx_path)
            
            # Buscar ocurrencias de cada clave en el texto
            found_replacements = {}
            total_text = ""
            
            # Extraer todo el texto del documento
            for paragraph in doc.paragraphs:
                total_text += paragraph.text + "\n"
            
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        total_text += cell.text + "\n"
            
            # Contar ocurrencias de cada clave
            for key, value in replacements.items():
                count = total_text.count(key)
                if count > 0:
                    found_replacements[key] = {
                        'replacement_value': value,
                        'occurrences': count
                    }
            
            preview = {
                'total_replacements_requested': len(replacements),
                'replacements_found': len(found_replacements),
                'replacements_not_found': len(replacements) - len(found_replacements),
                'found_replacements': found_replacements,
                'document_info': self.get_document_info(docx_path)
            }
            
            return preview
            
        except Exception as e:
            raise ValueError(f"Error al previsualizar reemplazos: {str(e)}") 