# Lambda Replacer Patterns

Lambda function para procesar documentos DOCX aplicando reemplazos de texto y convertirlos a PDF desde archivos almacenados en S3. Basada en la funcionalidad del proyecto `gedsystool_replacer`.

## 🎯 Funcionalidades

- **Procesamiento de DOCX**: Descarga documentos desde S3, aplica reemplazos y convierte a PDF
- **Conversión a PDF**: Utiliza LibreOffice para convertir documentos DOCX procesados a PDF
- **Reemplazos de texto**: Utiliza `python-docx-replace` para reemplazos precisos en documentos Word
- **Almacenamiento flexible**: Guarda en directorio permanente o temporal según configuración
- **Integración S3**: Descarga automática de documentos y subida de resultados procesados
- **URLs prefirmadas**: Generación opcional de URLs temporales para descarga
- **Autenticación**: API Key requerida en header `x-api-key`
- **Seguridad**: Bucket name en header `x-bucket-name` por seguridad

## 🏗️ Arquitectura

- **Plataforma**: AWS Lambda containerizada
- **Arquitectura**: ARM64 (optimizada para rendimiento y costo)
- **Runtime**: Python 3.11
- **Memoria**: 1024 MB
- **Timeout**: 300 segundos (5 minutos)
- **Dependencias**: python-docx, python-docx-replace, boto3, LibreOffice

## 📁 Estructura S3

```
bucket/
├── borradores/              # Documentos DOCX de entrada (DOCX_INPUT_DIR)
├── documentos/              # Documentos PDF procesados (PDF_OUTPUT_DIR)
└── temporal/                # Documentos PDF temporales (PDF_TEMPORAL_DIR)
```

## 🔧 Variables de Entorno

| Variable | Descripción | Valor por defecto |
|----------|-------------|-------------------|
| `API_KEY` | Clave de autenticación | `DocxReplacer2024Key` |
| `S3_ENDPOINT_URL` | URL del servicio S3 | - |
| `S3_ACCESS_KEY` | Access key de S3 | - |
| `S3_SECRET_KEY` | Secret key de S3 | - |
| `S3_REGION` | Región de S3 | `us-east-1` |
| `DOCX_INPUT_DIR` | Directorio de documentos de entrada | `borradores` |
| `PDF_OUTPUT_DIR` | Directorio de documentos PDF procesados | `documentos` |
| `PDF_TEMPORAL_DIR` | Directorio de documentos PDF temporales | `temporal` |

## 📡 API

### Endpoint
- **Método**: POST
- **Autenticación**: Header `x-api-key`
- **Bucket**: Header `x-bucket-name`

### Request Body

```json
{
    "docx_filename": "plantilla.docx",
    "replacements": {
        "{{nombre}}": "Juan Pérez",
        "{{fecha}}": "2024-01-15",
        "{{numero}}": "DOC-001"
    },
    "temporal": true,
    "expiration_seconds": 3600
}
```

### Parámetros

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `docx_filename` | string | ✅ | Nombre del archivo DOCX en S3 (debe terminar en .docx). El PDF resultante tendrá el mismo nombre con extensión .pdf |
| `replacements` | object | ✅ | Diccionario con reemplazos {clave: valor} |
| `temporal` | boolean | ❌ | Si true, guarda en directorio temporal, si false en documentos (default: false) |
| `expiration_seconds` | integer | ❌ | Tiempo de expiración para URL prefirmada (0 = sin URL, default: 3600) |

### Response

```json
{
    "success": true,
    "message": "Documento procesado exitosamente y convertido a PDF",
    "bucket": "mi-bucket",
    "s3_key": "documentos/plantilla_15012024123456.pdf",
    "output_filename": "plantilla_15012024123456.pdf",
    "storage_location": "documentos",
    "temporal": false,
    "replacements_applied": 3,
    "timestamp": "2024-01-15T12:34:56Z",
    "presigned_url": "https://...",
    "expires_in_seconds": 3600
}
```

## 🚀 Despliegue

### Prerrequisitos

- AWS CLI configurado
- Docker instalado
- Permisos para crear recursos en AWS (ECR, Lambda, IAM)

### Pasos

1. **Clonar y navegar al directorio**:
   ```bash
   cd lambda_replacer_patterns
   ```

2. **Ejecutar script de despliegue**:
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

3. **Configurar credenciales S3 reales**:
   ```bash
   aws lambda update-function-configuration \
     --function-name replacer-patterns \
     --environment "Variables={API_KEY=DocxReplacer2024Key,S3_ENDPOINT_URL=https://tu-servicio-s3.com,S3_ACCESS_KEY=tu-access-key,S3_SECRET_KEY=tu-secret-key,S3_REGION=us-east-1,DOCX_INPUT_DIR=documentos,DOCX_OUTPUT_DIR=documentos-procesados}" \
     --region us-east-1
   ```

## 🧪 Ejemplos de Uso

### Ejemplo 1: Reemplazo básico con URL de descarga

```bash
curl -X POST "https://tu-function-url.lambda-url.us-east-1.on.aws/" \
  -H "Content-Type: application/json" \
  -H "x-api-key: DocxReplacer2024Key" \
  -H "x-bucket-name: mi-bucket" \
  -d '{
    "docx_filename": "plantilla-contrato.docx",
    "replacements": {
        "{{cliente}}": "Empresa ABC S.A.",
        "{{fecha_contrato}}": "2024-01-15",
        "{{numero_contrato}}": "CONT-2024-001",
        "{{direccion}}": "Calle Principal 123",
        "{{telefono}}": "+1234567890"
    },
    "temporal": false,
    "expiration_seconds": 7200
}'
```

### Ejemplo 2: Procesamiento con almacenamiento temporal

```bash
curl -X POST "https://tu-function-url.lambda-url.us-east-1.on.aws/" \
  -H "Content-Type: application/json" \
  -H "x-api-key: DocxReplacer2024Key" \
  -H "x-bucket-name: mi-bucket" \
  -d '{
    "docx_filename": "carta-plantilla.docx",
    "replacements": {
        "{{destinatario}}": "María García",
        "{{asunto}}": "Confirmación de reunión",
        "{{fecha}}": "2024-01-15",
        "{{hora}}": "14:30"
    },
    "temporal": true,
    "expiration_seconds": 0
}'
```

### Ejemplo 3: Reemplazo de múltiples variables

```bash
curl -X POST "https://tu-function-url.lambda-url.us-east-1.on.aws/" \
  -H "Content-Type: application/json" \
  -H "x-api-key: DocxReplacer2024Key" \
  -H "x-bucket-name: mi-bucket" \
  -d '{
    "docx_filename": "factura-plantilla.docx",
    "replacements": {
        "{{numero_factura}}": "FAC-2024-001",
        "{{fecha_emision}}": "2024-01-15",
        "{{cliente_nombre}}": "Juan Pérez",
        "{{cliente_direccion}}": "Av. Libertador 456",
        "{{cliente_telefono}}": "+987654321",
        "{{total}}": "$1,250.00",
        "{{subtotal}}": "$1,000.00",
        "{{iva}}": "$250.00"
    },
    "temporal": false,
    "expiration_seconds": 3600
}'
```

## 🔍 Troubleshooting

### Error: "API key inválido o faltante"
- Verificar que el header `x-api-key` esté presente
- Confirmar que el valor coincida con la variable de entorno `API_KEY`

### Error: "Header 'x-bucket-name' requerido"
- Agregar el header `x-bucket-name` con el nombre del bucket S3

### Error: "Documento no encontrado en S3"
- Verificar que el archivo existe en `{bucket}/documentos/{filename}`
- Confirmar que las credenciales S3 son correctas
- Verificar que el bucket y directorio existen

### Error: "El archivo debe tener extensión .docx"
- Asegurar que `docx_filename` termine en `.docx`
- El archivo PDF de salida se genera automáticamente con el mismo nombre pero extensión `.pdf`

### Error: "Los reemplazos deben ser un diccionario"
- Verificar que el campo `replacements` sea un objeto JSON válido
- Confirmar que todas las claves y valores sean strings

### Error de timeout
- Documentos muy grandes pueden requerir más tiempo
- Considerar aumentar el timeout de la función Lambda
- Verificar la velocidad de conexión con S3

## 🐳 Desarrollo Local

### Requisitos
- Docker instalado
- Docker Compose (opcional)
- AWS CLI (para pruebas con S3)

### Ejecutar con Docker

1. **Construir la imagen**:
   ```bash
   docker build -t lambda-replacer-patterns .
   ```

2. **Ejecutar el contenedor**:
   ```bash
   docker run -p 9000:8080 \
     -e API_KEY=DocxReplacer2024Key \
     -e S3_ENDPOINT_URL=http://localhost:4566 \
     -e S3_ACCESS_KEY=test \
     -e S3_SECRET_KEY=test \
     -e S3_REGION=us-east-1 \
     -e DOCX_INPUT_DIR=borradores \
     -e PDF_OUTPUT_DIR=documentos \
     -e PDF_TEMPORAL_DIR=temporal \
     lambda-replacer-patterns
   ```

3. **Probar la función localmente**:
   ```bash
   curl -X POST "http://localhost:9000/2015-03-31/functions/function/invocations" \
     -H "Content-Type: application/json" \
     -d '{
       "headers": {
         "x-api-key": "DocxReplacer2024Key",
         "x-bucket-name": "mi-bucket"
       },
       "body": "{\"docx_filename\":\"plantilla.docx\",\"replacements\":{\"{{nombre}}\":\"Juan Pérez\"},\"temporal\":false}"
     }'
   ```

### Ejecutar con Docker Compose

1. **Crear docker-compose.yml**:
   ```yaml
   version: '3.8'
   services:
     lambda:
       build: .
       ports:
         - "9000:8080"
       environment:
         - API_KEY=DocxReplacer2024Key
         - S3_ENDPOINT_URL=http://localstack:4566
         - S3_ACCESS_KEY=test
         - S3_SECRET_KEY=test
         - S3_REGION=us-east-1
         - DOCX_INPUT_DIR=borradores
         - PDF_OUTPUT_DIR=documentos
         - PDF_TEMPORAL_DIR=temporal
     localstack:
       image: localstack/localstack
       ports:
         - "4566:4566"
       environment:
         - SERVICES=s3
         - DEFAULT_REGION=us-east-1
   ```

2. **Iniciar servicios**:
   ```bash
   docker-compose up -d
   ```

3. **Configurar bucket de prueba**:
   ```bash
   aws --endpoint-url=http://localhost:4566 s3 mb s3://mi-bucket
   aws --endpoint-url=http://localhost:4566 s3api put-object --bucket mi-bucket --key borradores/
   aws --endpoint-url=http://localhost:4566 s3api put-object --bucket mi-bucket --key documentos/
   aws --endpoint-url=http://localhost:4566 s3api put-object --bucket mi-bucket --key temporal/
   ```

4. **Subir documento de prueba**:
   ```bash
   aws --endpoint-url=http://localhost:4566 s3 cp plantilla.docx s3://mi-bucket/borradores/
   ```

5. **Probar la función**:
   ```bash
   curl -X POST "http://localhost:9000/2015-03-31/functions/function/invocations" \
     -H "Content-Type: application/json" \
     -d '{
       "headers": {
         "x-api-key": "DocxReplacer2024Key",
         "x-bucket-name": "mi-bucket"
       },
       "body": "{\"docx_filename\":\"plantilla.docx\",\"replacements\":{\"{{nombre}}\":\"Juan Pérez\"},\"temporal\":false}"
     }'
   ```

### Verificación de Resultados

1. **Listar archivos en S3**:
   ```bash
   aws --endpoint-url=http://localhost:4566 s3 ls s3://mi-bucket/documentos/
   ```

2. **Descargar PDF generado**:
   ```bash
   aws --endpoint-url=http://localhost:4566 s3 cp s3://mi-bucket/documentos/plantilla.pdf ./
   ```

## 📊 Monitoreo

### CloudWatch Logs
```bash
aws logs tail /aws/lambda/replacer-patterns --follow
```

### Métricas importantes
- **Duration**: Tiempo de procesamiento
- **Memory Used**: Uso de memoria durante el procesamiento
- **Error Rate**: Tasa de errores
- **Invocations**: Número de invocaciones

## 🔒 Seguridad

- **Autenticación**: API Key obligatoria
- **Bucket isolation**: Bucket name en headers para aislamiento
- **Temporary files**: Limpieza automática de archivos temporales
- **Error handling**: No exposición de información sensible en errores

## 📝 Notas de Implementación

- Basado en la funcionalidad del endpoint `/replace` de `gedsystool_replacer`
- Mantiene compatibilidad con `python-docx-replace`
- Utiliza LibreOffice para conversión DOCX a PDF
- Sigue los mismos patrones que las otras lambdas del proyecto
- Optimizado para documentos de tamaño medio (< 50MB)
- Genera nombres únicos con timestamp para evitar colisiones
- El nombre del archivo PDF final se basa automáticamente en el nombre del DOCX de entrada

## 🎯 Casos de Uso

- **Generación de contratos**: Plantillas con datos de clientes
- **Cartas personalizadas**: Correspondencia masiva
- **Facturas**: Documentos comerciales con datos variables
- **Certificados**: Documentos oficiales personalizados
- **Reportes**: Documentos con datos dinámicos 