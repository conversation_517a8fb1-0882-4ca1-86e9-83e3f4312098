#!/bin/bash

# Script para actualizar la Lambda PresignNewFileFunction existente
# ARN: arn:aws:lambda:us-east-1:165354057769:function:PresignNewFileFunction

set -e

FUNCTION_NAME="PresignNewFileFunction"
REGION="us-east-1"

echo "🚀 Actualizando Lambda: $FUNCTION_NAME"
echo "📍 Región: $REGION"
echo ""

# Verificar que la función existe
echo "🔍 Verificando que la función Lambda existe..."
if aws lambda get-function --function-name "$FUNCTION_NAME" --region "$REGION" >/dev/null 2>&1; then
    echo "✅ Función encontrada: $FUNCTION_NAME"
else
    echo "❌ Error: La función $FUNCTION_NAME no existe en la región $REGION"
    exit 1
fi

# Crear archivo ZIP con el código actualizado
echo ""
echo "📦 Creando paquete de despliegue..."
rm -f lambda_function.zip
zip lambda_function.zip lambda_function.py

if [ ! -f "lambda_function.zip" ]; then
    echo "❌ Error: No se pudo crear el archivo ZIP"
    exit 1
fi

echo "✅ Paquete creado: lambda_function.zip"

# Actualizar el código de la función
echo ""
echo "🔄 Actualizando código de la función Lambda..."
aws lambda update-function-code \
    --function-name "$FUNCTION_NAME" \
    --zip-file fileb://lambda_function.zip \
    --region "$REGION"

if [ $? -eq 0 ]; then
    echo "✅ Código actualizado exitosamente"
else
    echo "❌ Error al actualizar el código"
    exit 1
fi

# Obtener información actualizada de la función
echo ""
echo "📊 Información de la función actualizada:"
aws lambda get-function \
    --function-name "$FUNCTION_NAME" \
    --region "$REGION" \
    --query '{
        FunctionName: Configuration.FunctionName,
        Runtime: Configuration.Runtime,
        LastModified: Configuration.LastModified,
        CodeSize: Configuration.CodeSize,
        State: Configuration.State,
        Version: Configuration.Version
    }' \
    --output table

# Verificar si tiene Function URL configurada
echo ""
echo "🔗 Verificando Function URL..."
FUNCTION_URL=$(aws lambda get-function-url-config \
    --function-name "$FUNCTION_NAME" \
    --region "$REGION" \
    --query 'FunctionUrl' \
    --output text 2>/dev/null || echo "No configurada")

if [ "$FUNCTION_URL" != "No configurada" ]; then
    echo "✅ Function URL activa: $FUNCTION_URL"
else
    echo "⚠️  Function URL no configurada"
    echo "💡 Para crear una Function URL, ejecuta:"
    echo "   aws lambda create-function-url-config --function-name $FUNCTION_NAME --auth-type NONE --region $REGION"
fi

# Limpiar archivos temporales
echo ""
echo "🧹 Limpiando archivos temporales..."
rm -f lambda_function.zip
echo "✅ Limpieza completada"

echo ""
echo "🎉 Actualización completada exitosamente!"
echo ""
echo "📋 Funcionalidad actualizada:"
echo "• ✅ Recibe schema: {\"type\": \"borrador|pdf\", \"expiration\": number}"
echo "• ✅ Genera UUID v4 automáticamente"
echo "• ✅ Mapeo: borrador -> .docx, pdf -> .pdf"
echo "• ✅ Estructura: documentos/{uuid}.{extension}"
echo "• ✅ Invoca s3-presigned-url-generator con operation=upload"
echo ""
echo "📖 Ejemplo de uso:"
echo "curl -X POST \$FUNCTION_URL \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"x-api-key: tu-api-key\" \\"
echo "  -H \"x-bucket-name: gedsys-development\" \\"
echo "  -d '{\"type\": \"pdf\", \"expiration\": 3600}'"
echo ""
echo "📝 Para más información, consulta el README.md" 