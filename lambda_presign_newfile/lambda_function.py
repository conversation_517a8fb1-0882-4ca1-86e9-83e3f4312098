import boto3
import json
import uuid
from typing import Dict, Any, Optional

# Mapeo de tipos de documento fijos a extensiones
TYPE_EXTENSIONS = {
    "borrador": "docx",
    "pdf": "pdf"
}

# Extensiones permitidas para anexos
ALLOWED_ANEXO_EXTENSIONS = {
    # Documentos de oficina
    "docx", "pdf", "xlsx", "pptx", "txt", "csv",
    # Multimedia (simplificado según feedback)
    "mp3", "mp4",
    # Imágenes
    "jpg", "jpeg", "png", "gif",
    # Comprimidos (para todo lo demás)
    "zip"
}

# Mapeo de tipos a directorios
DIRECTORY_MAPPING = {
    "borrador": "documentos",
    "pdf": "documentos",
    "anexo": "anexos"
}

def generate_filepath(document_type: str, extension: Optional[str] = None) -> str:
    """
    Genera un filepath único basado en el tipo de documento
    
    Args:
        document_type: Tipo de documento ("borrador", "pdf" o "anexo")
        extension: Extensión del archivo (solo requerida para anexos)
        
    Returns:
        Filepath en formato: {directorio}/{uuid}.{extension}
        
    Raises:
        ValueError: Si el tipo de documento no es válido o faltan parámetros
    """
    # Validar tipo de documento
    if document_type not in DIRECTORY_MAPPING:
        valid_types = list(DIRECTORY_MAPPING.keys())
        raise ValueError(f"Tipo de documento no válido: {document_type}. Tipos permitidos: {valid_types}")
    
    # Generar UUID único
    file_uuid = str(uuid.uuid4())
    
    # Obtener directorio base
    directory = DIRECTORY_MAPPING[document_type]
    
    # Determinar extensión según el tipo
    if document_type == "anexo":
        if not extension:
            raise ValueError("Campo 'extension' es requerido para anexos")
        
        # Normalizar extensión (remover punto si existe, convertir a minúsculas)
        normalized_extension = extension.lower().lstrip('.')
        
        if normalized_extension not in ALLOWED_ANEXO_EXTENSIONS:
            raise ValueError(f"Extensión '{extension}' no permitida. Extensiones válidas: {sorted(ALLOWED_ANEXO_EXTENSIONS)}")
        file_extension = normalized_extension
    else:
        # Usar mapeo fijo para borradores y PDFs
        file_extension = TYPE_EXTENSIONS[document_type]
    
    return f"{directory}/{file_uuid}.{file_extension}"

def validate_request_body(body: Dict[str, Any]) -> None:
    """
    Valida el cuerpo de la request
    
    Args:
        body: Diccionario con los datos del body
        
    Raises:
        ValueError: Si la validación falla
    """
    # Validar que type esté presente
    if 'type' not in body:
        raise ValueError("Campo 'type' es requerido")
    
    document_type = body['type']
    
    # Validar que type sea válido
    if document_type not in DIRECTORY_MAPPING:
        valid_types = list(DIRECTORY_MAPPING.keys())
        raise ValueError(f"Tipo '{document_type}' no válido. Tipos permitidos: {valid_types}")
    
    # Validar extension para anexos
    if document_type == "anexo":
        if 'extension' not in body:
            raise ValueError("Campo 'extension' es requerido para anexos")
        
        extension = body['extension']
        if not isinstance(extension, str) or not extension.strip():
            raise ValueError("Campo 'extension' debe ser una cadena no vacía")
        
        # Normalizar extensión (remover punto si existe, convertir a minúsculas)
        normalized_extension = extension.lower().lstrip('.')
        if normalized_extension not in ALLOWED_ANEXO_EXTENSIONS:
            raise ValueError(f"Extensión '{extension}' no permitida. Extensiones válidas: {sorted(ALLOWED_ANEXO_EXTENSIONS)}")
    
    # Validar expiration si está presente
    if 'expiration' in body:
        expiration = body['expiration']
        if not isinstance(expiration, (int, float)) or expiration <= 0:
            raise ValueError("Campo 'expiration' debe ser un número positivo")

def lambda_handler(event: Dict[str, Any], context) -> Dict[str, Any]:
    """
    Handler principal que recibe type, extension (para anexos) y expiration,
    genera UUID y filepath, e invoca la lambda s3-presigned-url-generator
    
    Entrada esperada:
    {
        "headers": {
            "x-api-key": "...",
            "x-bucket-name": "..."
        },
        "body": {
            "type": "borrador|pdf|anexo",
            "extension": "docx|pdf|mp3|..." (solo para anexos),
            "expiration": 3600
        }
    }
    """
    lambda_client = boto3.client('lambda')
    
    try:
        # Parsear correctamente el body
        body_str = event.get('body', '{}')
        
        # Si el body es string, parsearlo a dict
        if isinstance(body_str, str):
            payload = json.loads(body_str)
        else:
            payload = body_str
        
        # Validar el body de la request
        validate_request_body(payload)
        
        # Extraer bucket_name de los headers
        headers = event.get('headers', {})
        bucket_name = headers.get('x-bucket-name')
        
        if not bucket_name:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'error': 'Missing x-bucket-name header'
                })
            }
        
        # Extraer parámetros del payload
        document_type = payload['type']
        expiration = payload.get('expiration', 3600)
        
        # Extraer extension si es anexo, normalizar
        extension = None
        if document_type == "anexo":
            extension = payload['extension'].lower().lstrip('.')
        
        # Generar filepath único
        filepath = generate_filepath(document_type, extension)
        
        # Construir payload para la lambda s3-presigned-url-generator
        s3_payload = {
            'operation': 'upload',
            'filepath': filepath,
            'expiration': expiration,
            'bucket_name': bucket_name
        }
        
        print(f"Generando URL para documento tipo '{document_type}' -> {filepath}")
        print(f"Payload a enviar a s3-presigned-url-generator: {s3_payload}")
        
        # Invocar la lambda s3-presigned-url-generator
        response = lambda_client.invoke(
            FunctionName='s3-presigned-url-generator',
            InvocationType='RequestResponse',
            Payload=json.dumps(s3_payload)
        )
        
        # Procesar respuesta
        result = json.loads(response['Payload'].read())
        print(f"Respuesta recibida de s3-presigned-url-generator: {result}")
        
        return result
        
    except json.JSONDecodeError as e:
        return {
            'statusCode': 400,
            'body': json.dumps({
                'error': 'Invalid JSON in request body',
                'details': str(e)
            })
        }
    except ValueError as e:
        return {
            'statusCode': 400,
            'body': json.dumps({
                'error': str(e)
            })
        }
    except Exception as e:
        print(f"Error inesperado: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Error interno del servidor',
                'details': str(e)
            })
        }