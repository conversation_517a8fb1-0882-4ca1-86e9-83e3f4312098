# Lambda PresignNewFileFunction

Lambda de AWS que simplifica la generación de URLs prefirmadas para subir nuevos archivos a S3. Recibe el tipo de documento, extensión (para anexos) y tiempo de expiración, genera automáticamente un UUID único y delega la generación de URL a la lambda `s3-presigned-url-generator`.

## 📋 Características

- ✅ **Interfaz simplificada**: Solo requiere tipo de documento y expiración
- ✅ **Generación automática de UUID v4** para nombres únicos de archivo
- ✅ **Mapeo inteligente de extensiones**:
  - `borrador` → `.docx` (documentos Word para edición)
  - `pdf` → `.pdf` (documentos finales)
  - `anexo` → extensión variable (13 tipos soportados)
- ✅ **Estructura de directorios estandarizada**:
  - `documentos/{uuid}.{extension}` para borradores y PDFs
  - `anexos/{uuid}.{extension}` para anexos
- ✅ **Soporte para múltiples tipos de archivo**:
  - Documentos: docx, pdf, xlsx, pptx, txt, csv
  - Multimedia: mp3, mp4
  - Imágenes: jpg, jpeg, png, gif
  - Comprimidos: zip
- ✅ **Normalización automática de extensiones** (mayúsculas/minúsculas, puntos)
- ✅ **Validación estricta** de parámetros de entrada
- ✅ **Integración transparente** con `s3-presigned-url-generator`
- ✅ **Manejo robusto de errores** con mensajes descriptivos
- ✅ **Compatibilidad hacia atrás** con requests existentes

## 🏗️ Arquitectura

```
Cliente → PresignNewFileFunction → s3-presigned-url-generator → S3
```

La lambda actúa como un proxy inteligente que:
1. Recibe una request simplificada del cliente
2. Genera un UUID único y construye el filepath
3. Determina el directorio según el tipo de documento
4. Invoca `s3-presigned-url-generator` con los parámetros correctos
5. Retorna la respuesta directamente al cliente

## 🚀 Uso

### Headers Requeridos

| Header | Descripción | Ejemplo |
|--------|-------------|---------|
| `x-api-key` | Clave de autenticación | `58en2lsmRUeHAfGHxkr2jlWiQcxdDQtXVTQtdmqt` |
| `x-bucket-name` | Nombre del bucket S3 | `gedsys-development` |

### Schema de Entrada

```json
{
  "type": "borrador|pdf|anexo",
  "extension": "docx|pdf|mp3|..." (solo para anexos),
  "expiration": 3600
}
```

### Parámetros

| Parámetro | Tipo | Requerido | Descripción |
|-----------|------|-----------|-------------|
| `type` | string | ✅ | Tipo de documento: `"borrador"`, `"pdf"` o `"anexo"` |
| `extension` | string | ⚠️ | Extensión del archivo (solo requerida para `type: "anexo"`) |
| `expiration` | number | ❌ | Tiempo de expiración en segundos (default: 3600) |

### Extensiones Soportadas para Anexos

| Categoría | Extensiones |
|-----------|-------------|
| **Documentos** | `docx`, `pdf`, `xlsx`, `pptx`, `txt`, `csv` |
| **Multimedia** | `mp3`, `mp4` |
| **Imágenes** | `jpg`, `jpeg`, `png`, `gif` |
| **Comprimidos** | `zip` |

## 📝 Ejemplos de Uso

### Generar URL para Borrador (DOCX)

```bash
curl -X POST https://tu-function-url.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: 58en2lsmRUeHAfGHxkr2jlWiQcxdDQtXVTQtdmqt" \
  -H "x-bucket-name: gedsys-development" \
  -d '{
    "type": "borrador",
    "expiration": 3600
  }'
```

**Resultado interno:**
- UUID generado: `b81edb63-aed9-4498-b572-03b38f297228`
- Filepath: `documentos/b81edb63-aed9-4498-b572-03b38f297228.docx`
- Operation: `upload`

### Generar URL para PDF

```bash
curl -X POST https://tu-function-url.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: 58en2lsmRUeHAfGHxkr2jlWiQcxdDQtXVTQtdmqt" \
  -H "x-bucket-name: gedsys-development" \
  -d '{
    "type": "pdf",
    "expiration": 1800
  }'
```

**Resultado interno:**
- UUID generado: `f47ac10b-58cc-4372-a567-0e02b2c3d479`
- Filepath: `documentos/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf`
- Operation: `upload`

### Generar URL para Anexo Excel

```bash
curl -X POST https://tu-function-url.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: 58en2lsmRUeHAfGHxkr2jlWiQcxdDQtXVTQtdmqt" \
  -H "x-bucket-name: gedsys-development" \
  -d '{
    "type": "anexo",
    "extension": "xlsx",
    "expiration": 3600
  }'
```

**Resultado interno:**
- UUID generado: `ca8690f0-2a04-4f36-924d-f1d04f907894`
- Filepath: `anexos/ca8690f0-2a04-4f36-924d-f1d04f907894.xlsx`
- Operation: `upload`

### Generar URL para Anexo Audio

```bash
curl -X POST https://tu-function-url.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: 58en2lsmRUeHAfGHxkr2jlWiQcxdDQtXVTQtdmqt" \
  -H "x-bucket-name: gedsys-development" \
  -d '{
    "type": "anexo",
    "extension": "mp3",
    "expiration": 1800
  }'
```

**Resultado interno:**
- UUID generado: `93adfe6d-c119-4a8b-8772-004fee522844`
- Filepath: `anexos/93adfe6d-c119-4a8b-8772-004fee522844.mp3`
- Operation: `upload`

### Generar URL para Anexo Comprimido

```bash
curl -X POST https://tu-function-url.lambda-url.us-east-1.on.aws/ \
  -H "Content-Type: application/json" \
  -H "x-api-key: 58en2lsmRUeHAfGHxkr2jlWiQcxdDQtXVTQtdmqt" \
  -H "x-bucket-name: gedsys-development" \
  -d '{
    "type": "anexo",
    "extension": "zip",
    "expiration": 7200
  }'
```

**Resultado interno:**
- UUID generado: `39cdc62e-f1d3-41fe-bb1e-5ba9fb978d8d`
- Filepath: `anexos/39cdc62e-f1d3-41fe-bb1e-5ba9fb978d8d.zip`
- Operation: `upload`

## 📤 Respuesta

La lambda retorna exactamente la misma respuesta que `s3-presigned-url-generator`:

### Respuesta Exitosa (200)

```json
{
  "statusCode": 200,
  "body": "{
    \"url\": \"https://gedsys-development.s3.amazonaws.com/documentos/uuid.docx?AWSAccessKeyId=...\",
    \"operation\": \"upload\",
    \"bucket\": \"gedsys-development\",
    \"filepath\": \"documentos/b81edb63-aed9-4498-b572-03b38f297228.docx\",
    \"expiration_seconds\": 3600,
    \"addressing_style\": \"path\",
    \"security\": \"authenticated\"
  }"
}
```

### Respuestas de Error

#### Error de Validación - Tipo Inválido (400)

```json
{
  "statusCode": 400,
  "body": "{
    \"error\": \"Tipo 'documento' no válido. Tipos permitidos: ['borrador', 'pdf', 'anexo']\"
  }"
}
```

#### Error de Validación - Anexo sin Extensión (400)

```json
{
  "statusCode": 400,
  "body": "{
    \"error\": \"Campo 'extension' es requerido para anexos\"
  }"
}
```

#### Error de Validación - Extensión No Permitida (400)

```json
{
  "statusCode": 400,
  "body": "{
    \"error\": \"Extensión 'exe' no permitida. Extensiones válidas: ['csv', 'docx', 'gif', 'jpeg', 'jpg', 'mp3', 'mp4', 'pdf', 'png', 'pptx', 'txt', 'xlsx', 'zip']\"
  }"
}
```

#### Error de Headers (400)

```json
{
  "statusCode": 400,
  "body": "{
    \"error\": \"Missing x-bucket-name header\"
  }"
}
```

## 🔧 Transformación de Datos

### Flujo de Transformación - Borrador

```
Entrada del Cliente:
{
  "type": "borrador",
  "expiration": 3600
}

↓ Procesamiento Interno ↓

Request a s3-presigned-url-generator:
{
  "operation": "upload",
  "filepath": "documentos/b81edb63-aed9-4498-b572-03b38f297228.docx",
  "expiration": 3600,
  "bucket_name": "gedsys-development"
}
```

### Flujo de Transformación - Anexo

```
Entrada del Cliente:
{
  "type": "anexo",
  "extension": "XLSX",
  "expiration": 3600
}

↓ Procesamiento Interno ↓
↓ Normalización: "XLSX" → "xlsx" ↓

Request a s3-presigned-url-generator:
{
  "operation": "upload",
  "filepath": "anexos/ca8690f0-2a04-4f36-924d-f1d04f907894.xlsx",
  "expiration": 3600,
  "bucket_name": "gedsys-development"
}
```

### Mapeo de Tipos y Directorios

| Tipo Cliente | Directorio | Extensión | Uso Típico |
|--------------|------------|-----------|------------|
| `borrador` | `documentos/` | `.docx` | Documentos en edición, colaboración |
| `pdf` | `documentos/` | `.pdf` | Documentos finales, firmados |
| `anexo` | `anexos/` | Variable | Archivos adjuntos, multimedia, datos |

### Normalización de Extensiones

La lambda normaliza automáticamente las extensiones:
- **Puntos**: `.PDF` → `pdf`, `XLSX` → `xlsx`
- **Mayúsculas**: `MP3` → `mp3`, `ZIP` → `zip`
- **Mixto**: `.Mp4` → `mp4`, `.DOCX` → `docx`

## 🚀 Despliegue

### Actualizar Lambda Existente

```bash
cd lambda_presign_newfile
./deploy_update.sh
```

El script:
1. ✅ Verifica que la lambda existe
2. ✅ Crea el paquete ZIP con el código actualizado
3. ✅ Actualiza el código de la función
4. ✅ Muestra información de la función actualizada
5. ✅ Verifica configuración de Function URL

### Información de la Lambda

- **Nombre**: `PresignNewFileFunction`
- **ARN**: `arn:aws:lambda:us-east-1:165354057769:function:PresignNewFileFunction`
- **Región**: `us-east-1`

## 🔐 Seguridad

### Autenticación

La lambda hereda el sistema de autenticación de `s3-presigned-url-generator`:
- Requiere header `x-api-key` válido
- Valida `x-bucket-name` en headers (no en body)

### Validaciones

1. **Tipo de documento**: Solo acepta `"borrador"`, `"pdf"` o `"anexo"`
2. **Extensión para anexos**: Lista blanca de 13 extensiones seguras
3. **Expiración**: Debe ser número positivo si se especifica
4. **Headers requeridos**: `x-api-key` y `x-bucket-name`

### Lista Blanca de Extensiones

Por seguridad, solo se permiten extensiones específicas para anexos:
- ✅ **Documentos seguros**: docx, pdf, xlsx, pptx, txt, csv
- ✅ **Multimedia estándar**: mp3, mp4
- ✅ **Imágenes comunes**: jpg, jpeg, png, gif
- ✅ **Archivos comprimidos**: zip
- ❌ **Ejecutables**: exe, bat, sh, etc. (bloqueados)
- ❌ **Scripts**: js, py, php, etc. (bloqueados)

## 🔄 Compatibilidad

### Compatibilidad Hacia Atrás

✅ **Requests existentes siguen funcionando sin cambios**

```json
// ✅ Funciona igual que antes
{
  "type": "borrador",
  "expiration": 3600
}

// ✅ Funciona igual que antes  
{
  "type": "pdf",
  "expiration": 1800
}
```

### Migración

No se requiere migración para clientes existentes. La nueva funcionalidad de anexos es completamente opt-in.

## 🧪 Casos de Uso

### Documentos Oficiales
- **Borradores**: Documentos en proceso de edición
- **PDFs**: Documentos finales para firma o archivo

### Anexos Multimedia
- **Audio**: Grabaciones, notas de voz
- **Video**: Presentaciones, tutoriales
- **Imágenes**: Capturas, diagramas, fotos

### Anexos de Datos
- **Excel**: Reportes, análisis de datos
- **CSV**: Exportaciones de datos
- **ZIP**: Múltiples archivos comprimidos

### Anexos de Oficina
- **PowerPoint**: Presentaciones
- **Word**: Documentos adicionales
- **TXT**: Notas, logs, configuraciones

## 📊 Métricas y Monitoreo

### Logs de CloudWatch

La lambda registra información detallada:
```
Generando URL para documento tipo 'anexo' -> anexos/uuid.xlsx
Payload a enviar a s3-presigned-url-generator: {...}
Respuesta recibida de s3-presigned-url-generator: {...}
```

### Métricas Recomendadas

- Invocaciones por tipo de documento
- Errores de validación por extensión
- Tiempo de respuesta promedio
- Uso por directorio (documentos/ vs anexos/)

## 🔧 Desarrollo

### Estructura del Proyecto

```
lambda_presign_newfile/
├── lambda_function.py      # Código principal
├── requirements.txt        # Dependencias
├── deploy_update.sh       # Script de despliegue
├── test_anexos.py         # Pruebas locales
└── README.md              # Esta documentación
```

### Pruebas Locales

```bash
python test_anexos.py
```

Ejecuta pruebas completas de:
- ✅ Generación de filepaths
- ✅ Validaciones de entrada
- ✅ Normalización de extensiones
- ✅ Manejo de errores

## 📈 Roadmap

### Funcionalidades Futuras Consideradas

- [ ] Soporte para más tipos de archivo (previa evaluación de seguridad)
- [ ] Validación de tamaño máximo por tipo
- [ ] Metadatos personalizados en S3
- [ ] Integración con antivirus para anexos

### Limitaciones Actuales

- Máximo 13 extensiones para anexos (por diseño de seguridad)
- Solo operación `upload` (no `download`)
- Dependiente de `s3-presigned-url-generator`

---

## 📞 Soporte

Para reportar problemas o solicitar nuevas funcionalidades, contactar al equipo de desarrollo.

**Versión**: 2.0.0 (con soporte para anexos)  
**Última actualización**: Enero 2025 